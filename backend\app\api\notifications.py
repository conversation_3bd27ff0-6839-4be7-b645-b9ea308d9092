"""通知管理API"""
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta

from fastapi import APIRouter, Depends, HTTPException, status, Query
from pydantic import BaseModel, validator
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, desc

from ..core.auth import get_current_user
from ..models import User, NotificationHistory
from ..notifications import notification_manager
from ..database import get_db_session
from ..utils import get_logger

logger = get_logger("notifications_api")

router = APIRouter(prefix="/api/v1/notifications", tags=["通知管理"])


class NotificationPreferences(BaseModel):
    """通知偏好设置"""
    notify_live_start: bool = True
    notify_live_end: bool = True
    notify_title_change: bool = True
    enabled_channels: List[str] = ["wxpusher"]
    quiet_hours: Optional[Dict[str, Any]] = None
    
    @validator('enabled_channels')
    def validate_channels(cls, v):
        valid_channels = ['wxpusher', 'email', 'sms', 'qq_bot']
        for channel in v:
            if channel not in valid_channels:
                raise ValueError(f'不支持的通知渠道: {channel}')
        return v
    
    @validator('quiet_hours')
    def validate_quiet_hours(cls, v):
        if v is None:
            return v
        
        if not isinstance(v, dict):
            raise ValueError('免打扰时段必须是字典格式')
        
        if v.get('enabled', False):
            start_time = v.get('start_time')
            end_time = v.get('end_time')
            
            if not start_time or not end_time:
                raise ValueError('启用免打扰时段时必须设置开始和结束时间')
            
            # 验证时间格式
            try:
                from datetime import time
                time.fromisoformat(start_time)
                time.fromisoformat(end_time)
            except ValueError:
                raise ValueError('时间格式错误，应为 HH:MM 格式')
        
        return v


class NotificationHistoryResponse(BaseModel):
    """通知历史响应"""
    id: str
    notification_type: str
    notification_type_display: str
    channel: str
    channel_display: str
    message: str
    status: str
    status_display: str
    error_message: str = None
    created_at: str
    sent_at: str = None
    streamer: Optional[Dict[str, Any]] = None


class TestNotificationRequest(BaseModel):
    """测试通知请求"""
    channel: str
    
    @validator('channel')
    def validate_channel(cls, v):
        valid_channels = ['wxpusher', 'email']
        if v not in valid_channels:
            raise ValueError(f'不支持的通知渠道: {v}')
        return v


@router.get("/preferences", response_model=NotificationPreferences, summary="获取通知偏好设置")
async def get_notification_preferences(
    current_user: User = Depends(get_current_user)
):
    """获取用户的通知偏好设置"""
    preferences = current_user.get_notification_preferences()
    return NotificationPreferences(**preferences)


@router.put("/preferences", summary="更新通知偏好设置")
async def update_notification_preferences(
    preferences: NotificationPreferences,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
):
    """
    更新通知偏好设置
    
    - **notify_live_start**: 是否通知开播
    - **notify_live_end**: 是否通知下播
    - **notify_title_change**: 是否通知标题变更
    - **enabled_channels**: 启用的通知渠道列表
    - **quiet_hours**: 免打扰时段设置
    """
    try:
        # 更新用户通知偏好
        current_user.set_notification_preferences(preferences.dict())
        await db.commit()
        
        logger.info(f"用户 {current_user.username} 更新通知偏好设置")
        
        return {"message": "通知偏好设置更新成功"}
        
    except Exception as e:
        await db.rollback()
        logger.error(f"更新通知偏好设置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="设置更新失败，请稍后重试"
        )


@router.get("/history", response_model=List[NotificationHistoryResponse], summary="获取通知历史记录")
async def get_notification_history(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session),
    limit: int = Query(20, le=100, description="返回记录数量"),
    offset: int = Query(0, ge=0, description="偏移量"),
    notification_type: Optional[str] = Query(None, description="通知类型筛选"),
    channel: Optional[str] = Query(None, description="通知渠道筛选"),
    status_filter: Optional[str] = Query(None, alias="status", description="状态筛选")
):
    """
    获取通知历史记录
    
    - **limit**: 返回记录数量（最大100）
    - **offset**: 偏移量
    - **notification_type**: 通知类型筛选（live_start、live_end、title_change）
    - **channel**: 通知渠道筛选（wxpusher、email）
    - **status**: 状态筛选（pending、sent、failed）
    """
    try:
        # 构建查询
        stmt = select(NotificationHistory).where(
            NotificationHistory.user_id == current_user.id
        )
        
        # 应用筛选条件
        if notification_type:
            stmt = stmt.where(NotificationHistory.notification_type == notification_type)
        
        if channel:
            stmt = stmt.where(NotificationHistory.channel == channel)
        
        if status_filter:
            stmt = stmt.where(NotificationHistory.status == status_filter)
        
        # 排序和分页
        stmt = stmt.order_by(desc(NotificationHistory.created_at)).offset(offset).limit(limit)
        
        result = await db.execute(stmt)
        notifications = result.scalars().all()
        
        # 构造响应
        responses = []
        for notification in notifications:
            response_data = notification.to_detailed_dict()
            responses.append(NotificationHistoryResponse(**response_data))
        
        return responses
        
    except Exception as e:
        logger.error(f"获取通知历史失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取通知历史失败"
        )


@router.post("/test", summary="发送测试通知")
async def send_test_notification(
    request: TestNotificationRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
):
    """
    发送测试通知
    
    - **channel**: 通知渠道（wxpusher、email）
    """
    try:
        # 检查通知渠道是否可用
        if not notification_manager.is_channel_available(request.channel):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"通知渠道不可用: {request.channel}"
            )
        
        # 生成测试消息
        test_message = f"这是来自直播监控系统的测试通知\n\n发送时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n如果您收到此消息，说明通知功能正常工作。"
        
        # 发送测试通知
        results = await notification_manager.send_notification(
            user_id=str(current_user.id),
            channels=[request.channel],
            notification_type="test",
            message=test_message
        )
        
        success = results.get(request.channel, False)
        
        # 记录通知历史
        notification = NotificationHistory.create_notification(
            user_id=str(current_user.id),
            notification_type="test",
            channel=request.channel,
            message=test_message
        )
        
        if success:
            notification.mark_as_sent()
        else:
            notification.mark_as_failed("测试通知发送失败")
        
        db.add(notification)
        await db.commit()
        
        if success:
            logger.info(f"用户 {current_user.username} 测试通知发送成功: {request.channel}")
            return {"message": "测试通知发送成功"}
        else:
            logger.warning(f"用户 {current_user.username} 测试通知发送失败: {request.channel}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="测试通知发送失败"
            )
        
    except HTTPException:
        await db.rollback()
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"发送测试通知失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="发送测试通知失败，请稍后重试"
        )


@router.get("/channels", summary="获取可用的通知渠道")
async def get_available_channels():
    """获取可用的通知渠道列表"""
    try:
        channels = notification_manager.get_available_channels()
        
        channel_info = []
        for channel in channels:
            channel_obj = notification_manager.get_channel(channel)
            if channel_obj:
                channel_info.append({
                    "name": channel,
                    "display_name": channel_obj.get_display_name(),
                    "enabled": channel_obj.is_enabled()
                })
        
        return {
            "channels": channel_info,
            "total": len(channel_info)
        }
        
    except Exception as e:
        logger.error(f"获取通知渠道失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取通知渠道失败"
        )


@router.get("/stats", summary="获取通知统计")
async def get_notification_stats(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session),
    days: int = Query(7, ge=1, le=30, description="统计天数")
):
    """
    获取通知统计信息
    
    - **days**: 统计天数（1-30天）
    """
    try:
        # 计算时间范围
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=days)
        
        # 查询通知历史
        stmt = select(NotificationHistory).where(
            and_(
                NotificationHistory.user_id == current_user.id,
                NotificationHistory.created_at >= start_date,
                NotificationHistory.created_at <= end_date
            )
        )
        
        result = await db.execute(stmt)
        notifications = result.scalars().all()
        
        # 统计数据
        total_count = len(notifications)
        sent_count = sum(1 for n in notifications if n.is_sent())
        failed_count = sum(1 for n in notifications if n.is_failed())
        pending_count = sum(1 for n in notifications if n.is_pending())
        
        # 按类型统计
        type_stats = {}
        for notification in notifications:
            ntype = notification.notification_type
            if ntype not in type_stats:
                type_stats[ntype] = {"total": 0, "sent": 0, "failed": 0}
            
            type_stats[ntype]["total"] += 1
            if notification.is_sent():
                type_stats[ntype]["sent"] += 1
            elif notification.is_failed():
                type_stats[ntype]["failed"] += 1
        
        # 按渠道统计
        channel_stats = {}
        for notification in notifications:
            channel = notification.channel
            if channel not in channel_stats:
                channel_stats[channel] = {"total": 0, "sent": 0, "failed": 0}
            
            channel_stats[channel]["total"] += 1
            if notification.is_sent():
                channel_stats[channel]["sent"] += 1
            elif notification.is_failed():
                channel_stats[channel]["failed"] += 1
        
        return {
            "period": {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat(),
                "days": days
            },
            "summary": {
                "total": total_count,
                "sent": sent_count,
                "failed": failed_count,
                "pending": pending_count,
                "success_rate": round(sent_count / total_count * 100, 2) if total_count > 0 else 0
            },
            "by_type": type_stats,
            "by_channel": channel_stats
        }
        
    except Exception as e:
        logger.error(f"获取通知统计失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取通知统计失败"
        )
