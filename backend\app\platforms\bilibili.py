"""B站平台适配器"""
from typing import Dict, Any, List, Optional
from datetime import datetime

from .base import BasePlatform
from ..utils import get_logger

logger = get_logger("bilibili")


class BilibiliPlatform(BasePlatform):
    """B站平台适配器"""

    def __init__(self, base_url: str = "https://api.live.bilibili.com", rate_limit: int = 60):
        super().__init__("bilibili", base_url, rate_limit)

    async def get_streamer_status(self, streamer_id: str) -> Dict[str, Any]:
        """获取B站主播状态"""
        try:
            # 获取房间信息
            room_info = await self._get_room_info(streamer_id)
            if not room_info:
                raise Exception(f"房间不存在: {streamer_id}")

            # 获取主播信息
            user_info = await self._get_user_info(room_info.get("uid"))

            # 构造标准化状态
            status = {
                'platform': self.platform_name,
                'streamer_id': streamer_id,
                'streamer_name': user_info.get('uname', '未知主播'),
                'is_live': room_info.get('live_status') == 1,
                'title': room_info.get('title', ''),
                'viewer_count': room_info.get('online', 0),
                'live_url': f"https://live.bilibili.com/{streamer_id}",
                'cover_image': room_info.get('user_cover', ''),
                'avatar_url': user_info.get('face', ''),
                'profile_url': f"https://space.bilibili.com/{room_info.get('uid', '')}",
                'start_time': room_info.get('live_time', ''),
                'last_updated': datetime.utcnow().isoformat()
            }

            logger.debug(f"B站主播状态获取成功: {streamer_id}, 直播状态: {status['is_live']}")
            return status

        except Exception as e:
            logger.error(f"获取B站主播状态失败: {streamer_id}, 错误: {e}")
            raise

    async def _get_room_info(self, room_id: str) -> Optional[Dict[str, Any]]:
        """获取房间信息"""
        url = "/room/v1/Room/get_info"
        params = {"room_id": room_id}

        try:
            response = await self._make_request(url, params)
            
            if response.get("code") != 0:
                error_msg = response.get("message", "未知错误")
                logger.warning(f"B站房间信息获取失败: {room_id}, 错误: {error_msg}")
                return None

            return response.get("data", {})

        except Exception as e:
            logger.error(f"B站房间信息请求失败: {room_id}, 错误: {e}")
            return None

    async def _get_user_info(self, uid: str) -> Dict[str, Any]:
        """获取用户信息"""
        if not uid:
            return {}

        url = "/live_user/v1/Master/info"
        params = {"uid": uid}

        try:
            response = await self._make_request(url, params)
            
            if response.get("code") != 0:
                logger.warning(f"B站用户信息获取失败: {uid}")
                return {}

            user_data = response.get("data", {})
            return user_data.get("info", {})

        except Exception as e:
            logger.error(f"B站用户信息请求失败: {uid}, 错误: {e}")
            return {}

    async def search_streamer(self, keyword: str) -> List[Dict[str, Any]]:
        """搜索B站主播"""
        url = "/room/v1/Area/getList"
        params = {
            "platform": "web",
            "parent_area_id": 0,
            "area_id": 0,
            "sort_type": "",
            "page": 1,
            "page_size": 20
        }

        try:
            response = await self._make_request(url, params)
            
            if response.get("code") != 0:
                logger.warning(f"B站主播搜索失败: {keyword}")
                return []

            data = response.get("data", {})
            room_list = data.get("list", [])

            results = []
            for room in room_list:
                if keyword.lower() in room.get("uname", "").lower() or \
                   keyword.lower() in room.get("title", "").lower():
                    
                    result = {
                        'platform': self.platform_name,
                        'streamer_id': str(room.get("roomid", "")),
                        'streamer_name': room.get("uname", ""),
                        'title': room.get("title", ""),
                        'is_live': True,  # 搜索结果都是正在直播的
                        'viewer_count': room.get("online", 0),
                        'cover_image': room.get("user_cover", ""),
                        'avatar_url': room.get("face", ""),
                        'live_url': f"https://live.bilibili.com/{room.get('roomid', '')}",
                        'profile_url': f"https://space.bilibili.com/{room.get('uid', '')}"
                    }
                    results.append(result)

            logger.debug(f"B站主播搜索完成: {keyword}, 找到 {len(results)} 个结果")
            return results

        except Exception as e:
            logger.error(f"B站主播搜索失败: {keyword}, 错误: {e}")
            return []

    async def get_room_id_by_uid(self, uid: str) -> Optional[str]:
        """通过UID获取房间ID"""
        url = "/room/v1/Room/getRoomInfoOld"
        params = {"mid": uid}

        try:
            response = await self._make_request(url, params)
            
            if response.get("code") != 0:
                return None

            data = response.get("data", {})
            return str(data.get("roomid", ""))

        except Exception as e:
            logger.error(f"通过UID获取房间ID失败: {uid}, 错误: {e}")
            return None

    async def validate_streamer_id(self, streamer_id: str) -> bool:
        """验证B站房间ID是否有效"""
        try:
            room_info = await self._get_room_info(streamer_id)
            return room_info is not None
        except Exception:
            return False

    def _get_default_headers(self) -> Dict[str, str]:
        """获取B站专用请求头"""
        headers = super()._get_default_headers()
        headers.update({
            'Referer': 'https://live.bilibili.com/',
            'Origin': 'https://live.bilibili.com'
        })
        return headers
