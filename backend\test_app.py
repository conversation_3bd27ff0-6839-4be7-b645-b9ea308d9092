"""应用测试脚本"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.utils import get_settings, setup_logging
from app.database import init_database, create_tables, close_database
from app.platforms import platform_manager
from app.notifications import notification_manager

logger = setup_logging()


async def test_database():
    """测试数据库连接"""
    try:
        logger.info("测试数据库连接...")
        await init_database()
        await create_tables()
        logger.info("✅ 数据库连接测试成功")
        return True
    except Exception as e:
        logger.error(f"❌ 数据库连接测试失败: {e}")
        return False


async def test_platforms():
    """测试平台适配器"""
    try:
        logger.info("测试平台适配器...")
        await platform_manager.initialize()
        
        # 测试B站平台
        bilibili = platform_manager.get_platform('bilibili')
        if bilibili:
            # 测试一个知名主播的房间（这里使用一个通常存在的房间ID）
            status = await bilibili.get_streamer_status('1')
            logger.info(f"✅ B站平台测试成功: {status.get('streamer_name', '未知')}")
        
        await platform_manager.close()
        return True
    except Exception as e:
        logger.error(f"❌ 平台适配器测试失败: {e}")
        return False


async def test_notifications():
    """测试通知系统"""
    try:
        logger.info("测试通知系统...")
        await notification_manager.initialize()
        
        # 测试可用渠道
        channels = notification_manager.get_available_channels()
        logger.info(f"✅ 通知系统测试成功，可用渠道: {channels}")
        
        await notification_manager.close()
        return True
    except Exception as e:
        logger.error(f"❌ 通知系统测试失败: {e}")
        return False


async def test_config():
    """测试配置系统"""
    try:
        logger.info("测试配置系统...")
        settings = get_settings()
        logger.info(f"✅ 配置系统测试成功")
        logger.info(f"  - 应用名称: {settings.app.name}")
        logger.info(f"  - 应用版本: {settings.app.version}")
        logger.info(f"  - 调试模式: {settings.app.debug}")
        return True
    except Exception as e:
        logger.error(f"❌ 配置系统测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    logger.info("🚀 开始应用测试...")
    
    tests = [
        ("配置系统", test_config),
        ("数据库", test_database),
        ("平台适配器", test_platforms),
        ("通知系统", test_notifications),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n📋 测试 {test_name}...")
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 清理资源
    try:
        await close_database()
    except:
        pass
    
    # 输出测试结果
    logger.info("\n📊 测试结果汇总:")
    success_count = 0
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        logger.info(f"  - {test_name}: {status}")
        if success:
            success_count += 1
    
    total_tests = len(results)
    logger.info(f"\n🎯 测试完成: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        logger.info("🎉 所有测试通过！应用可以正常启动。")
        return 0
    else:
        logger.error("💥 部分测试失败，请检查配置和依赖。")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
