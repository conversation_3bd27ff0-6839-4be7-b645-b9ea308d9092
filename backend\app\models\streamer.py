"""主播模型"""
import json
import uuid
from datetime import datetime
from typing import Dict, Any, Optional

from sqlalchemy import Column, String, DateTime, Text, UniqueConstraint
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from ..database import Base


class Streamer(Base):
    """主播模型"""
    __tablename__ = 'streamers'

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    platform = Column(String(20), nullable=False, index=True)
    streamer_id = Column(String(100), nullable=False)  # 平台上的主播ID
    streamer_name = Column(String(100), nullable=False)
    avatar_url = Column(String(500))
    profile_url = Column(String(500))
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 最新状态缓存
    last_status = Column(Text)  # JSON字符串
    last_checked = Column(DateTime)

    # 关系
    subscriptions = relationship("Subscription", back_populates="streamer", cascade="all, delete-orphan")
    notification_history = relationship("NotificationHistory", back_populates="streamer")

    # 唯一约束：同一平台的主播ID唯一
    __table_args__ = (
        UniqueConstraint('platform', 'streamer_id', name='uq_platform_streamer_id'),
    )

    def __repr__(self) -> str:
        return f"<Streamer(id={self.id}, platform={self.platform}, streamer_id={self.streamer_id}, name={self.streamer_name})>"

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': str(self.id),
            'platform': self.platform,
            'streamer_id': self.streamer_id,
            'streamer_name': self.streamer_name,
            'avatar_url': self.avatar_url,
            'profile_url': self.profile_url,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'last_checked': self.last_checked.isoformat() if self.last_checked else None,
            'last_status': self.get_last_status()
        }

    def get_last_status(self) -> Optional[Dict[str, Any]]:
        """获取最新状态"""
        if self.last_status:
            try:
                return json.loads(self.last_status)
            except json.JSONDecodeError:
                pass
        return None

    def set_last_status(self, status: Dict[str, Any]) -> None:
        """设置最新状态"""
        self.last_status = json.dumps(status, ensure_ascii=False)
        self.last_checked = datetime.utcnow()

    def update_status(self, status: Dict[str, Any]) -> bool:
        """更新状态并检查是否有变化
        
        Args:
            status: 新的状态信息
            
        Returns:
            是否有状态变化
        """
        old_status = self.get_last_status()
        self.set_last_status(status)
        
        # 检查是否有重要变化
        if not old_status:
            return True  # 首次检查
        
        # 检查直播状态变化
        if old_status.get('is_live') != status.get('is_live'):
            return True
        
        # 检查标题变化
        if old_status.get('title') != status.get('title'):
            return True
        
        return False

    def is_live(self) -> bool:
        """是否正在直播"""
        status = self.get_last_status()
        return status.get('is_live', False) if status else False

    def get_live_title(self) -> str:
        """获取直播标题"""
        status = self.get_last_status()
        return status.get('title', '') if status else ''

    def get_viewer_count(self) -> int:
        """获取观看人数"""
        status = self.get_last_status()
        return status.get('viewer_count', 0) if status else 0

    def get_live_url(self) -> str:
        """获取直播链接"""
        status = self.get_last_status()
        if status and status.get('live_url'):
            return status['live_url']
        
        # 根据平台生成默认链接
        if self.platform == 'bilibili':
            return f"https://live.bilibili.com/{self.streamer_id}"
        elif self.platform == 'douyu':
            return f"https://www.douyu.com/{self.streamer_id}"
        elif self.platform == 'huya':
            return f"https://www.huya.com/{self.streamer_id}"
        
        return ""

    def get_platform_display_name(self) -> str:
        """获取平台显示名称"""
        platform_names = {
            'bilibili': 'B站',
            'douyu': '斗鱼',
            'huya': '虎牙'
        }
        return platform_names.get(self.platform, self.platform)

    def to_status_dict(self) -> Dict[str, Any]:
        """转换为包含状态的字典"""
        base_dict = self.to_dict()
        status = self.get_last_status()
        
        if status:
            base_dict.update({
                'is_live': status.get('is_live', False),
                'title': status.get('title', ''),
                'viewer_count': status.get('viewer_count', 0),
                'live_url': self.get_live_url(),
                'cover_image': status.get('cover_image', ''),
                'start_time': status.get('start_time', '')
            })
        else:
            base_dict.update({
                'is_live': False,
                'title': '',
                'viewer_count': 0,
                'live_url': self.get_live_url(),
                'cover_image': '',
                'start_time': ''
            })
        
        return base_dict
