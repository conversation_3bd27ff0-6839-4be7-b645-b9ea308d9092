"""主应用入口"""
import asyncio
from contextlib import asynccontextmanager
from typing import Dict, Any

from fastapi import FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException

from .api import api_router
from .core import monitor_service
from .database import init_database, close_database, create_tables
from .utils import get_settings, setup_logging, get_logger

# 设置日志
logger = setup_logging()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    logger.info("应用启动中...")
    
    try:
        # 初始化数据库
        await init_database()
        logger.info("数据库初始化完成")
        
        # 创建数据库表（如果不存在）
        await create_tables()
        logger.info("数据库表检查完成")
        
        # 启动监控服务
        await monitor_service.start_monitoring()
        logger.info("监控服务启动完成")
        
        logger.info("应用启动成功")
        
    except Exception as e:
        logger.error(f"应用启动失败: {e}")
        raise
    
    yield
    
    logger.info("应用关闭中...")
    
    try:
        # 停止监控服务
        await monitor_service.stop_monitoring()
        logger.info("监控服务已停止")
        
        # 关闭数据库连接
        await close_database()
        logger.info("数据库连接已关闭")
        
        logger.info("应用关闭完成")
        
    except Exception as e:
        logger.error(f"应用关闭异常: {e}")


def create_app() -> FastAPI:
    """创建FastAPI应用"""
    settings = get_settings()
    
    app = FastAPI(
        title=settings.app.name,
        version=settings.app.version,
        description="一个现代化的直播监控系统，支持多平台主播状态监控和多渠道通知推送",
        docs_url="/docs" if settings.app.debug else None,
        redoc_url="/redoc" if settings.app.debug else None,
        lifespan=lifespan
    )
    
    # 配置CORS
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"] if settings.app.debug else ["http://localhost:3000", "http://localhost:8080"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # 注册路由
    app.include_router(api_router)
    
    # 注册异常处理器
    register_exception_handlers(app)
    
    # 注册中间件
    register_middleware(app)
    
    return app


def register_exception_handlers(app: FastAPI) -> None:
    """注册异常处理器"""
    
    @app.exception_handler(HTTPException)
    async def http_exception_handler(request: Request, exc: HTTPException):
        """HTTP异常处理"""
        logger.warning(f"HTTP异常: {exc.status_code} - {exc.detail}")
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "success": False,
                "error": {
                    "code": f"HTTP_{exc.status_code}",
                    "message": exc.detail
                },
                "timestamp": get_current_timestamp()
            }
        )
    
    @app.exception_handler(StarletteHTTPException)
    async def starlette_http_exception_handler(request: Request, exc: StarletteHTTPException):
        """Starlette HTTP异常处理"""
        logger.warning(f"Starlette HTTP异常: {exc.status_code} - {exc.detail}")
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "success": False,
                "error": {
                    "code": f"HTTP_{exc.status_code}",
                    "message": exc.detail
                },
                "timestamp": get_current_timestamp()
            }
        )
    
    @app.exception_handler(RequestValidationError)
    async def validation_exception_handler(request: Request, exc: RequestValidationError):
        """请求验证异常处理"""
        logger.warning(f"请求验证异常: {exc.errors()}")
        return JSONResponse(
            status_code=422,
            content={
                "success": False,
                "error": {
                    "code": "VALIDATION_ERROR",
                    "message": "请求参数验证失败",
                    "details": exc.errors()
                },
                "timestamp": get_current_timestamp()
            }
        )
    
    @app.exception_handler(Exception)
    async def general_exception_handler(request: Request, exc: Exception):
        """通用异常处理"""
        logger.error(f"未处理的异常: {type(exc).__name__}: {exc}")
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "error": {
                    "code": "INTERNAL_SERVER_ERROR",
                    "message": "服务器内部错误"
                },
                "timestamp": get_current_timestamp()
            }
        )


def register_middleware(app: FastAPI) -> None:
    """注册中间件"""
    
    @app.middleware("http")
    async def logging_middleware(request: Request, call_next):
        """请求日志中间件"""
        import time
        
        start_time = time.time()
        
        # 记录请求
        logger.info(f"请求开始: {request.method} {request.url}")
        
        # 处理请求
        response = await call_next(request)
        
        # 计算处理时间
        process_time = time.time() - start_time
        
        # 记录响应
        logger.info(f"请求完成: {request.method} {request.url} - {response.status_code} - {process_time:.3f}s")
        
        # 添加处理时间头
        response.headers["X-Process-Time"] = str(process_time)
        
        return response


def get_current_timestamp() -> str:
    """获取当前时间戳"""
    from datetime import datetime
    return datetime.utcnow().isoformat() + "Z"


# 创建应用实例
app = create_app()


# 根路径
@app.get("/", tags=["根路径"])
async def root():
    """根路径"""
    settings = get_settings()
    return {
        "success": True,
        "data": {
            "name": settings.app.name,
            "version": settings.app.version,
            "status": "running",
            "docs_url": "/docs" if settings.app.debug else None
        },
        "timestamp": get_current_timestamp()
    }


# 健康检查
@app.get("/health", tags=["健康检查"])
async def health_check():
    """健康检查"""
    try:
        # 检查数据库连接
        from .database import db_manager
        # 这里可以添加更多的健康检查逻辑
        
        return {
            "success": True,
            "data": {
                "status": "healthy",
                "services": {
                    "database": "ok",
                    "monitor": "ok" if monitor_service.running else "stopped",
                    "cache": "ok"
                }
            },
            "timestamp": get_current_timestamp()
        }
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return JSONResponse(
            status_code=503,
            content={
                "success": False,
                "error": {
                    "code": "SERVICE_UNAVAILABLE",
                    "message": "服务不可用"
                },
                "timestamp": get_current_timestamp()
            }
        )


if __name__ == "__main__":
    import uvicorn
    
    settings = get_settings()
    
    uvicorn.run(
        "app.main:app",
        host=settings.app.host,
        port=settings.app.port,
        reload=settings.app.debug,
        log_level="debug" if settings.app.debug else "info"
    )
