"""平台适配器基类"""
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
from datetime import datetime

from ..utils import HTTPClient, get_logger

logger = get_logger("platform")


class BasePlatform(ABC):
    """平台适配器基类"""

    def __init__(self, platform_name: str, base_url: str, rate_limit: int = 60):
        """
        Args:
            platform_name: 平台名称
            base_url: 基础URL
            rate_limit: 速率限制（每分钟请求数）
        """
        self.platform_name = platform_name
        self.base_url = base_url
        self.rate_limit = rate_limit
        self.client: Optional[HTTPClient] = None

    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.start()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close()

    async def start(self) -> None:
        """启动平台适配器"""
        if self.client is None:
            self.client = HTTPClient(
                base_url=self.base_url,
                rate_limit=self.rate_limit,
                headers=self._get_default_headers()
            )
            await self.client.start()
            logger.info(f"{self.platform_name} 平台适配器已启动")

    async def close(self) -> None:
        """关闭平台适配器"""
        if self.client:
            await self.client.close()
            self.client = None
            logger.info(f"{self.platform_name} 平台适配器已关闭")

    def _get_default_headers(self) -> Dict[str, str]:
        """获取默认请求头"""
        return {
            'User-Agent': 'LiveMonitor/1.0',
            'Accept': 'application/json',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8'
        }

    @abstractmethod
    async def get_streamer_status(self, streamer_id: str) -> Dict[str, Any]:
        """
        获取主播状态

        Args:
            streamer_id: 主播ID

        Returns:
            Dict包含以下字段:
            - platform: 平台名称
            - streamer_id: 主播ID
            - streamer_name: 主播名称
            - is_live: 是否正在直播
            - title: 直播标题
            - viewer_count: 观看人数
            - live_url: 直播链接
            - cover_image: 封面图片
            - start_time: 开播时间
            - last_updated: 最后更新时间
        """
        pass

    @abstractmethod
    async def search_streamer(self, keyword: str) -> List[Dict[str, Any]]:
        """
        搜索主播

        Args:
            keyword: 搜索关键词

        Returns:
            主播列表
        """
        pass

    async def validate_streamer_id(self, streamer_id: str) -> bool:
        """
        验证主播ID是否有效

        Args:
            streamer_id: 主播ID

        Returns:
            是否有效
        """
        try:
            status = await self.get_streamer_status(streamer_id)
            return status is not None
        except Exception as e:
            logger.warning(f"验证主播ID失败 {self.platform_name}:{streamer_id}, 错误: {e}")
            return False

    async def get_streamer_info(self, streamer_id: str) -> Optional[Dict[str, Any]]:
        """
        获取主播基本信息

        Args:
            streamer_id: 主播ID

        Returns:
            主播信息
        """
        try:
            status = await self.get_streamer_status(streamer_id)
            if status:
                return {
                    'platform': self.platform_name,
                    'streamer_id': streamer_id,
                    'streamer_name': status.get('streamer_name', ''),
                    'avatar_url': status.get('avatar_url', ''),
                    'profile_url': status.get('profile_url', ''),
                }
            return None
        except Exception as e:
            logger.error(f"获取主播信息失败 {self.platform_name}:{streamer_id}, 错误: {e}")
            return None

    def _normalize_status(self, raw_data: Dict[str, Any], streamer_id: str) -> Dict[str, Any]:
        """
        标准化状态数据

        Args:
            raw_data: 原始数据
            streamer_id: 主播ID

        Returns:
            标准化后的状态数据
        """
        return {
            'platform': self.platform_name,
            'streamer_id': streamer_id,
            'streamer_name': raw_data.get('streamer_name', ''),
            'is_live': raw_data.get('is_live', False),
            'title': raw_data.get('title', ''),
            'viewer_count': raw_data.get('viewer_count', 0),
            'live_url': raw_data.get('live_url', ''),
            'cover_image': raw_data.get('cover_image', ''),
            'avatar_url': raw_data.get('avatar_url', ''),
            'profile_url': raw_data.get('profile_url', ''),
            'start_time': raw_data.get('start_time', ''),
            'last_updated': datetime.utcnow().isoformat()
        }

    async def _make_request(self, url: str, params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        发起HTTP请求

        Args:
            url: 请求URL
            params: 请求参数

        Returns:
            响应数据

        Raises:
            Exception: 请求失败时抛出
        """
        if not self.client:
            await self.start()

        try:
            response = await self.client.get(url, params=params)
            return response
        except Exception as e:
            logger.error(f"{self.platform_name} 请求失败: {url}, 错误: {e}")
            raise

    def get_platform_display_name(self) -> str:
        """获取平台显示名称"""
        platform_names = {
            'bilibili': 'B站',
            'douyu': '斗鱼',
            'huya': '虎牙'
        }
        return platform_names.get(self.platform_name, self.platform_name)

    def generate_live_url(self, streamer_id: str) -> str:
        """生成直播链接"""
        if self.platform_name == 'bilibili':
            return f"https://live.bilibili.com/{streamer_id}"
        elif self.platform_name == 'douyu':
            return f"https://www.douyu.com/{streamer_id}"
        elif self.platform_name == 'huya':
            return f"https://www.huya.com/{streamer_id}"
        return ""

    def generate_profile_url(self, streamer_id: str) -> str:
        """生成主播主页链接"""
        if self.platform_name == 'bilibili':
            return f"https://space.bilibili.com/{streamer_id}"
        elif self.platform_name == 'douyu':
            return f"https://www.douyu.com/{streamer_id}"
        elif self.platform_name == 'huya':
            return f"https://www.huya.com/{streamer_id}"
        return ""
