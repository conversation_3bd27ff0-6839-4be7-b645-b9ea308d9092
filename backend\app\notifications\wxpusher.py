"""WxPusher微信推送通知渠道"""
from typing import Dict, Any, Optional, List
import json

from .base import BaseNotificationChannel
from ..utils import HTTPClient, get_settings, get_logger

logger = get_logger("wxpusher")


class WxPusherChannel(BaseNotificationChannel):
    """WxPusher微信推送通知渠道"""

    def __init__(self):
        super().__init__("wxpusher")
        self.settings = get_settings()
        self.app_token = self.settings.notifications.wxpusher_app_token
        self.base_url = self.settings.notifications.wxpusher_base_url
        self.client: Optional[HTTPClient] = None

    async def _get_client(self) -> HTTPClient:
        """获取HTTP客户端"""
        if not self.client:
            self.client = HTTPClient(
                base_url=self.base_url,
                timeout=30,
                headers={'Content-Type': 'application/json'}
            )
            await self.client.start()
        return self.client

    async def send_message(self, user_id: str, message: str, **kwargs) -> bool:
        """
        发送微信推送消息

        Args:
            user_id: 用户ID（这里应该是WxPusher的UID）
            message: 消息内容
            **kwargs: 额外参数
                - summary: 消息摘要
                - content_type: 内容类型（1=文本，2=html，3=markdown）
                - topic_ids: 主题ID列表
                - uids: 用户UID列表
                - url: 点击跳转URL

        Returns:
            是否发送成功
        """
        try:
            client = await self._get_client()

            # 构造请求数据
            data = {
                "appToken": self.app_token,
                "content": message,
                "summary": kwargs.get("summary", message[:50] + "..." if len(message) > 50 else message),
                "contentType": kwargs.get("content_type", 1),  # 默认文本格式
            }

            # 设置接收者
            if kwargs.get("topic_ids"):
                data["topicIds"] = kwargs["topic_ids"]
            else:
                # 默认发送给指定用户
                data["uids"] = kwargs.get("uids", [user_id])

            # 设置跳转URL
            if kwargs.get("url"):
                data["url"] = kwargs["url"]

            # 发送请求
            response = await client.post("/send/message", json=data)

            # 检查响应
            if response.get("success"):
                logger.info(f"WxPusher消息发送成功: 用户={user_id}")
                return True
            else:
                error_msg = response.get("msg", "未知错误")
                logger.error(f"WxPusher消息发送失败: 用户={user_id}, 错误={error_msg}")
                return False

        except Exception as e:
            logger.error(f"WxPusher消息发送异常: 用户={user_id}, 错误: {e}")
            return False

    async def validate_config(self) -> bool:
        """验证WxPusher配置"""
        if not self.app_token:
            logger.error("WxPusher App Token未配置")
            return False

        try:
            client = await self._get_client()
            
            # 测试API连接
            response = await client.get("/fun/wxuser", params={"appToken": self.app_token})
            
            if response.get("success"):
                logger.info("WxPusher配置验证成功")
                return True
            else:
                logger.error(f"WxPusher配置验证失败: {response.get('msg', '未知错误')}")
                return False

        except Exception as e:
            logger.error(f"WxPusher配置验证异常: {e}")
            return False

    async def get_user_list(self) -> List[Dict[str, Any]]:
        """获取关注用户列表"""
        try:
            client = await self._get_client()
            
            response = await client.get("/fun/wxuser", params={"appToken": self.app_token})
            
            if response.get("success"):
                return response.get("data", [])
            else:
                logger.error(f"获取WxPusher用户列表失败: {response.get('msg', '未知错误')}")
                return []

        except Exception as e:
            logger.error(f"获取WxPusher用户列表异常: {e}")
            return []

    async def create_qr_code(self, extra: str = "") -> Optional[Dict[str, Any]]:
        """
        创建关注二维码

        Args:
            extra: 额外参数，用于标识用户

        Returns:
            二维码信息
        """
        try:
            client = await self._get_client()
            
            data = {
                "appToken": self.app_token,
                "extra": extra,
                "validTime": 1800  # 30分钟有效期
            }
            
            response = await client.post("/fun/create/qr", json=data)
            
            if response.get("success"):
                qr_data = response.get("data", {})
                logger.info(f"WxPusher二维码创建成功: {extra}")
                return qr_data
            else:
                logger.error(f"WxPusher二维码创建失败: {response.get('msg', '未知错误')}")
                return None

        except Exception as e:
            logger.error(f"WxPusher二维码创建异常: {e}")
            return None

    async def send_streamer_notification(
        self,
        user_id: str,
        notification_type: str,
        streamer_name: str,
        platform_name: str,
        **kwargs
    ) -> bool:
        """
        发送主播通知

        Args:
            user_id: 用户ID
            notification_type: 通知类型
            streamer_name: 主播名称
            platform_name: 平台名称
            **kwargs: 其他参数

        Returns:
            是否发送成功
        """
        # 格式化消息
        message = self.format_message(notification_type, streamer_name, platform_name, **kwargs)
        
        # 设置消息摘要
        summary = f"【{platform_name}】{streamer_name}"
        if notification_type == 'live_start':
            summary += " 开播了"
        elif notification_type == 'live_end':
            summary += " 下播了"
        elif notification_type == 'title_change':
            summary += " 更新了标题"

        # 发送消息
        return await self.send_message(
            user_id,
            message,
            summary=summary,
            content_type=1,  # 文本格式
            url=kwargs.get('live_url')  # 点击跳转到直播间
        )

    async def close(self) -> None:
        """关闭客户端"""
        if self.client:
            await self.client.close()
            self.client = None
