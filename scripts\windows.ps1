# 直播监控系统 Windows PowerShell 脚本

param(
    [Parameter(Mandatory=$true)]
    [string]$Command
)

function Show-Help {
    Write-Host "直播监控系统 - 可用命令:" -ForegroundColor Green
    Write-Host ""
    Write-Host "开发相关:" -ForegroundColor Yellow
    Write-Host "  install     - 安装依赖"
    Write-Host "  test        - 运行测试"
    Write-Host "  dev         - 启动开发服务器"
    Write-Host "  lint        - 代码检查"
    Write-Host "  format      - 代码格式化"
    Write-Host ""
    Write-Host "构建相关:" -ForegroundColor Yellow
    Write-Host "  build       - 构建项目"
    Write-Host "  clean       - 清理构建文件"
    Write-Host ""
    Write-Host "Docker相关:" -ForegroundColor Yellow
    Write-Host "  docker-build - 构建Docker镜像"
    Write-Host "  docker-up   - 启动Docker服务"
    Write-Host "  docker-down - 停止Docker服务"
    Write-Host "  docker-logs - 查看Docker日志"
    Write-Host ""
    Write-Host "数据库相关:" -ForegroundColor Yellow
    Write-Host "  db-init     - 初始化数据库"
    Write-Host "  db-reset    - 重置数据库"
    Write-Host ""
    Write-Host "使用方法:" -ForegroundColor Cyan
    Write-Host "  .\scripts\windows.ps1 <command>"
    Write-Host "  例如: .\scripts\windows.ps1 docker-up"
}

function Install-Dependencies {
    Write-Host "📦 安装后端依赖..." -ForegroundColor Blue
    Set-Location backend
    pip install -r requirements.txt
    Set-Location ..
    Write-Host "✅ 依赖安装完成" -ForegroundColor Green
}

function Run-Test {
    Write-Host "🧪 运行应用测试..." -ForegroundColor Blue
    Set-Location backend
    python test_app.py
    Set-Location ..
}

function Start-Dev {
    Write-Host "🚀 启动开发服务器..." -ForegroundColor Blue
    python scripts/start_backend.py
}

function Run-Lint {
    Write-Host "🔍 运行代码检查..." -ForegroundColor Blue
    Set-Location backend
    python -m flake8 app/
    python -m mypy app/
    Set-Location ..
}

function Format-Code {
    Write-Host "🎨 格式化代码..." -ForegroundColor Blue
    Set-Location backend
    python -m black app/
    python -m isort app/
    Set-Location ..
}

function Build-Project {
    Write-Host "🔨 构建项目..." -ForegroundColor Blue
    # 这里可以添加构建步骤
    Write-Host "✅ 构建完成" -ForegroundColor Green
}

function Clean-Project {
    Write-Host "🧹 清理构建文件..." -ForegroundColor Blue
    Get-ChildItem -Path . -Recurse -Name "__pycache__" | Remove-Item -Recurse -Force
    Get-ChildItem -Path . -Recurse -Name "*.pyc" | Remove-Item -Force
    Get-ChildItem -Path . -Recurse -Name "*.pyo" | Remove-Item -Force
    Get-ChildItem -Path . -Recurse -Name "*.pyd" | Remove-Item -Force
    Get-ChildItem -Path . -Recurse -Name ".coverage" | Remove-Item -Force
    Get-ChildItem -Path . -Recurse -Name "*.egg-info" | Remove-Item -Recurse -Force
    Write-Host "✅ 清理完成" -ForegroundColor Green
}

function Docker-Build {
    Write-Host "🐳 构建Docker镜像..." -ForegroundColor Blue
    docker-compose build
}

function Docker-Up {
    Write-Host "🐳 启动Docker服务..." -ForegroundColor Blue
    docker-compose up -d
    Write-Host ""
    Write-Host "服务启动完成！访问地址：" -ForegroundColor Green
    Write-Host "前端: http://localhost:3000" -ForegroundColor Cyan
    Write-Host "后端API: http://localhost:8000" -ForegroundColor Cyan
    Write-Host "API文档: http://localhost:8000/docs" -ForegroundColor Cyan
}

function Docker-Down {
    Write-Host "🐳 停止Docker服务..." -ForegroundColor Blue
    docker-compose down
}

function Docker-Logs {
    Write-Host "📋 查看Docker日志..." -ForegroundColor Blue
    docker-compose logs -f
}

function DB-Init {
    Write-Host "🗄️  初始化数据库..." -ForegroundColor Blue
    docker-compose up -d postgres
    Write-Host "等待数据库启动..." -ForegroundColor Yellow
    Start-Sleep -Seconds 10
    Set-Location backend
    python -c "import asyncio; from app.database import create_tables; asyncio.run(create_tables())"
    Set-Location ..
    Write-Host "✅ 数据库初始化完成" -ForegroundColor Green
}

function DB-Reset {
    Write-Host "🗄️  重置数据库..." -ForegroundColor Blue
    docker-compose down postgres
    docker volume rm live_monitor-v2_postgres_data
    docker-compose up -d postgres
    Write-Host "等待数据库启动..." -ForegroundColor Yellow
    Start-Sleep -Seconds 10
    DB-Init
}

function Quick-Start {
    Write-Host "⚡ 快速启动开发环境..." -ForegroundColor Blue
    docker-compose up -d postgres redis
    Write-Host "等待服务启动..." -ForegroundColor Yellow
    Start-Sleep -Seconds 5
    Start-Dev
}

function Show-Status {
    Write-Host "📊 服务状态:" -ForegroundColor Blue
    docker-compose ps
}

# 主逻辑
switch ($Command.ToLower()) {
    "help" { Show-Help }
    "install" { Install-Dependencies }
    "test" { Run-Test }
    "dev" { Start-Dev }
    "lint" { Run-Lint }
    "format" { Format-Code }
    "build" { Build-Project }
    "clean" { Clean-Project }
    "docker-build" { Docker-Build }
    "docker-up" { Docker-Up }
    "docker-down" { Docker-Down }
    "docker-logs" { Docker-Logs }
    "db-init" { DB-Init }
    "db-reset" { DB-Reset }
    "quick-start" { Quick-Start }
    "status" { Show-Status }
    default {
        Write-Host "❌ 未知命令: $Command" -ForegroundColor Red
        Write-Host ""
        Show-Help
    }
}
