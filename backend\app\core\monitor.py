"""监控服务模块"""
import asyncio
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from ..models import User, Streamer, Subscription, NotificationHistory
from ..platforms import platform_manager
from ..notifications import notification_manager
from ..database import get_db_session
from ..utils import cache_service, streamer_cache_key, get_logger

logger = get_logger("monitor")


class MonitorService:
    """监控服务"""
    
    def __init__(self):
        self.running = False
        self.check_interval = 30  # 默认30秒检查一次
        self.max_concurrent_checks = 10
        self.retry_attempts = 3
        self.retry_delay = 5
        self._monitor_task: Optional[asyncio.Task] = None
    
    async def start_monitoring(self) -> None:
        """启动监控服务"""
        if self.running:
            logger.warning("监控服务已在运行")
            return
        
        self.running = True
        logger.info("监控服务启动中...")
        
        # 初始化依赖服务
        await platform_manager.initialize()
        await notification_manager.initialize()
        await cache_service.connect()
        
        # 启动监控任务
        self._monitor_task = asyncio.create_task(self._monitor_loop())
        
        logger.info("监控服务已启动")
    
    async def stop_monitoring(self) -> None:
        """停止监控服务"""
        if not self.running:
            return
        
        self.running = False
        logger.info("监控服务停止中...")
        
        # 取消监控任务
        if self._monitor_task:
            self._monitor_task.cancel()
            try:
                await self._monitor_task
            except asyncio.CancelledError:
                pass
        
        # 关闭依赖服务
        await platform_manager.close()
        await notification_manager.close()
        await cache_service.disconnect()
        
        logger.info("监控服务已停止")
    
    async def _monitor_loop(self) -> None:
        """监控循环"""
        while self.running:
            try:
                start_time = datetime.utcnow()
                
                # 执行一轮监控检查
                await self._check_all_streamers()
                
                # 计算耗时
                duration = (datetime.utcnow() - start_time).total_seconds()
                logger.debug(f"监控检查完成，耗时: {duration:.2f}s")
                
                # 等待下次检查
                await asyncio.sleep(self.check_interval)
                
            except asyncio.CancelledError:
                logger.info("监控循环被取消")
                break
            except Exception as e:
                logger.error(f"监控循环异常: {e}")
                await asyncio.sleep(5)  # 出错后短暂等待
    
    async def _check_all_streamers(self) -> None:
        """检查所有主播状态"""
        try:
            # 获取所有活跃的订阅
            subscriptions = await self._get_active_subscriptions()
            
            if not subscriptions:
                logger.debug("没有活跃的订阅")
                return
            
            logger.info(f"开始检查 {len(subscriptions)} 个订阅")
            
            # 按平台分组，批量检查
            platform_groups = self._group_by_platform(subscriptions)
            
            # 创建检查任务
            tasks = []
            for platform_name, platform_subscriptions in platform_groups.items():
                task = self._check_platform_streamers(platform_name, platform_subscriptions)
                tasks.append(task)
            
            # 并发执行所有平台的检查
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 统计结果
            success_count = sum(1 for r in results if not isinstance(r, Exception))
            logger.info(f"平台检查完成: {success_count}/{len(tasks)} 成功")
            
        except Exception as e:
            logger.error(f"检查所有主播失败: {e}")
    
    async def _get_active_subscriptions(self) -> List[Dict[str, Any]]:
        """获取所有活跃的订阅"""
        try:
            async for db in get_db_session():
                stmt = select(
                    Subscription.id,
                    Subscription.user_id,
                    Subscription.custom_settings,
                    Streamer.id.label('streamer_id'),
                    Streamer.platform,
                    Streamer.streamer_id.label('platform_streamer_id'),
                    Streamer.streamer_name,
                    User.id.label('user_db_id'),
                    User.notification_preferences
                ).select_from(
                    Subscription.__table__.join(Streamer.__table__).join(User.__table__)
                ).where(
                    Subscription.is_active == True,
                    User.is_active == True
                )
                
                result = await db.execute(stmt)
                rows = result.fetchall()
                
                subscriptions = []
                for row in rows:
                    subscriptions.append({
                        'subscription_id': str(row.id),
                        'user_id': str(row.user_id),
                        'streamer_id': str(row.streamer_id),
                        'platform': row.platform,
                        'platform_streamer_id': row.platform_streamer_id,
                        'streamer_name': row.streamer_name,
                        'custom_settings': row.custom_settings,
                        'notification_preferences': row.notification_preferences
                    })
                
                return subscriptions
                
        except Exception as e:
            logger.error(f"获取活跃订阅失败: {e}")
            return []
    
    def _group_by_platform(self, subscriptions: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """按平台分组订阅"""
        platform_groups = {}
        
        for subscription in subscriptions:
            platform = subscription['platform']
            if platform not in platform_groups:
                platform_groups[platform] = []
            platform_groups[platform].append(subscription)
        
        return platform_groups
    
    async def _check_platform_streamers(self, platform_name: str, subscriptions: List[Dict[str, Any]]) -> None:
        """检查特定平台的主播"""
        platform = platform_manager.get_platform(platform_name)
        if not platform:
            logger.warning(f"未找到平台适配器: {platform_name}")
            return
        
        # 按主播ID分组（避免重复检查同一主播）
        streamer_groups = {}
        for subscription in subscriptions:
            streamer_id = subscription['platform_streamer_id']
            if streamer_id not in streamer_groups:
                streamer_groups[streamer_id] = []
            streamer_groups[streamer_id].append(subscription)
        
        # 创建检查任务（限制并发数）
        semaphore = asyncio.Semaphore(self.max_concurrent_checks)
        tasks = []
        
        for streamer_id, streamer_subscriptions in streamer_groups.items():
            task = self._check_single_streamer(
                semaphore, platform, platform_name, streamer_id, streamer_subscriptions
            )
            tasks.append(task)
        
        # 执行检查任务
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 统计结果
        success_count = sum(1 for r in results if not isinstance(r, Exception))
        logger.debug(f"{platform_name} 平台检查完成: {success_count}/{len(tasks)} 成功")
    
    async def _check_single_streamer(
        self,
        semaphore: asyncio.Semaphore,
        platform,
        platform_name: str,
        streamer_id: str,
        subscriptions: List[Dict[str, Any]]
    ) -> None:
        """检查单个主播"""
        async with semaphore:
            try:
                # 获取当前状态
                current_status = await self._get_streamer_status_with_retry(
                    platform, streamer_id
                )
                
                if not current_status:
                    logger.warning(f"获取主播状态失败: {platform_name}:{streamer_id}")
                    return
                
                # 获取缓存的历史状态
                cache_key = streamer_cache_key(platform_name, streamer_id)
                previous_status = await cache_service.get(cache_key)
                
                # 检测状态变化
                if self._status_changed(previous_status, current_status):
                    logger.info(f"检测到状态变化: {platform_name}:{streamer_id}")
                    
                    # 发送通知
                    await self._send_notifications(subscriptions, previous_status, current_status)
                    
                    # 更新数据库中的主播状态
                    await self._update_streamer_status(subscriptions[0]['streamer_id'], current_status)
                
                # 更新缓存
                await cache_service.set(cache_key, current_status, ttl=3600)
                
            except Exception as e:
                logger.error(f"检查主播失败: {platform_name}:{streamer_id}, 错误: {e}")
    
    async def _get_streamer_status_with_retry(self, platform, streamer_id: str) -> Optional[Dict[str, Any]]:
        """带重试的获取主播状态"""
        for attempt in range(self.retry_attempts):
            try:
                return await platform.get_streamer_status(streamer_id)
            except Exception as e:
                if attempt < self.retry_attempts - 1:
                    logger.warning(f"获取主播状态失败，重试 {attempt + 1}/{self.retry_attempts}: {e}")
                    await asyncio.sleep(self.retry_delay)
                else:
                    logger.error(f"获取主播状态最终失败: {e}")
                    return None
    
    def _status_changed(self, old_status: Optional[Dict], new_status: Dict) -> bool:
        """检测状态是否发生变化"""
        if not old_status:
            return True  # 首次检查
        
        # 检查直播状态变化
        if old_status.get('is_live') != new_status.get('is_live'):
            return True
        
        # 检查标题变化（仅在直播时）
        if (new_status.get('is_live') and 
            old_status.get('title') != new_status.get('title')):
            return True
        
        return False
    
    async def _send_notifications(
        self,
        subscriptions: List[Dict[str, Any]],
        old_status: Optional[Dict],
        new_status: Dict
    ) -> None:
        """发送通知"""
        # 确定通知类型
        notification_type = self._get_notification_type(old_status, new_status)
        
        # 为每个订阅用户发送通知
        for subscription in subscriptions:
            try:
                await self._send_user_notification(subscription, notification_type, new_status)
            except Exception as e:
                logger.error(f"发送用户通知失败: {subscription['user_id']}, 错误: {e}")
    
    def _get_notification_type(self, old_status: Optional[Dict], new_status: Dict) -> str:
        """确定通知类型"""
        if not old_status:
            return 'status_update'
        
        old_live = old_status.get('is_live', False)
        new_live = new_status.get('is_live', False)
        
        if not old_live and new_live:
            return 'live_start'
        elif old_live and not new_live:
            return 'live_end'
        elif old_status.get('title') != new_status.get('title'):
            return 'title_change'
        else:
            return 'status_update'
    
    async def _send_user_notification(
        self,
        subscription: Dict[str, Any],
        notification_type: str,
        status: Dict[str, Any]
    ) -> None:
        """发送用户通知"""
        # 检查用户通知偏好
        if not self._should_send_notification(subscription, notification_type):
            return
        
        # 获取用户启用的通知渠道
        enabled_channels = self._get_enabled_channels(subscription)
        
        if not enabled_channels:
            logger.debug(f"用户未启用任何通知渠道: {subscription['user_id']}")
            return
        
        # 发送通知
        results = await notification_manager.send_streamer_notification(
            user_id=subscription['user_id'],
            channels=enabled_channels,
            notification_type=notification_type,
            streamer_name=subscription['streamer_name'],
            platform_name=platform_manager.get_platform_display_name(subscription['platform']),
            **status
        )
        
        # 记录通知历史
        await self._record_notification_history(subscription, notification_type, status, results)
    
    def _should_send_notification(self, subscription: Dict[str, Any], notification_type: str) -> bool:
        """检查是否应该发送通知"""
        # 解析用户通知偏好
        import json
        
        try:
            preferences = json.loads(subscription.get('notification_preferences', '{}'))
        except (json.JSONDecodeError, TypeError):
            preferences = {}
        
        # 检查免打扰时段
        if self._is_quiet_time(preferences):
            return False
        
        # 检查通知类型偏好
        type_mapping = {
            'live_start': 'notify_live_start',
            'live_end': 'notify_live_end',
            'title_change': 'notify_title_change'
        }
        
        pref_key = type_mapping.get(notification_type)
        if pref_key:
            return preferences.get(pref_key, True)
        
        return True
    
    def _is_quiet_time(self, preferences: Dict[str, Any]) -> bool:
        """检查是否在免打扰时段"""
        quiet_hours = preferences.get('quiet_hours', {})
        
        if not quiet_hours.get('enabled', False):
            return False
        
        from datetime import time
        
        try:
            now = datetime.now().time()
            start_time = time.fromisoformat(quiet_hours.get('start_time', '22:00'))
            end_time = time.fromisoformat(quiet_hours.get('end_time', '08:00'))
            
            if start_time <= end_time:
                return start_time <= now <= end_time
            else:  # 跨天的情况
                return now >= start_time or now <= end_time
        except ValueError:
            return False
    
    def _get_enabled_channels(self, subscription: Dict[str, Any]) -> List[str]:
        """获取启用的通知渠道"""
        import json
        
        try:
            preferences = json.loads(subscription.get('notification_preferences', '{}'))
        except (json.JSONDecodeError, TypeError):
            preferences = {}
        
        return preferences.get('enabled_channels', ['wxpusher'])
    
    async def _update_streamer_status(self, streamer_id: str, status: Dict[str, Any]) -> None:
        """更新数据库中的主播状态"""
        try:
            async for db in get_db_session():
                stmt = select(Streamer).where(Streamer.id == streamer_id)
                result = await db.execute(stmt)
                streamer = result.scalar_one_or_none()
                
                if streamer:
                    streamer.set_last_status(status)
                    await db.commit()
                    
        except Exception as e:
            logger.error(f"更新主播状态失败: {streamer_id}, 错误: {e}")
    
    async def _record_notification_history(
        self,
        subscription: Dict[str, Any],
        notification_type: str,
        status: Dict[str, Any],
        results: Dict[str, bool]
    ) -> None:
        """记录通知历史"""
        try:
            async for db in get_db_session():
                for channel, success in results.items():
                    notification = NotificationHistory.create_notification(
                        user_id=subscription['user_id'],
                        notification_type=notification_type,
                        channel=channel,
                        message=f"【{subscription['platform']}】{subscription['streamer_name']} {notification_type}",
                        streamer_id=subscription['streamer_id']
                    )
                    
                    if success:
                        notification.mark_as_sent()
                    else:
                        notification.mark_as_failed("发送失败")
                    
                    db.add(notification)
                
                await db.commit()
                
        except Exception as e:
            logger.error(f"记录通知历史失败: {e}")


# 全局监控服务实例
monitor_service = MonitorService()
