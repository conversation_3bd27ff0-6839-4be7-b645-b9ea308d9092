import { request } from './index'

// 通知偏好设置
export interface NotificationPreferences {
  notify_live_start: boolean
  notify_live_end: boolean
  notify_title_change: boolean
  enabled_channels: string[]
  quiet_hours?: {
    enabled: boolean
    start_time: string
    end_time: string
  }
}

// 通知历史记录
export interface NotificationHistory {
  id: string
  notification_type: string
  notification_type_display: string
  channel: string
  channel_display: string
  message: string
  status: string
  status_display: string
  error_message?: string
  created_at: string
  sent_at?: string
  streamer?: {
    id: string
    platform: string
    streamer_id: string
    streamer_name: string
    platform_display: string
  }
}

// 通知渠道信息
export interface NotificationChannel {
  name: string
  display_name: string
  enabled: boolean
}

// 通知统计
export interface NotificationStats {
  period: {
    start_date: string
    end_date: string
    days: number
  }
  summary: {
    total: number
    sent: number
    failed: number
    pending: number
    success_rate: number
  }
  by_type: Record<string, {
    total: number
    sent: number
    failed: number
  }>
  by_channel: Record<string, {
    total: number
    sent: number
    failed: number
  }>
}

// 测试通知请求
export interface TestNotificationRequest {
  channel: string
}

// 通知管理API
export const notificationsApi = {
  // 获取通知偏好设置
  getPreferences(): Promise<NotificationPreferences> {
    return request.get('/v1/notifications/preferences')
  },

  // 更新通知偏好设置
  updatePreferences(preferences: NotificationPreferences): Promise<{ message: string }> {
    return request.put('/v1/notifications/preferences', preferences)
  },

  // 获取通知历史记录
  getHistory(params?: {
    limit?: number
    offset?: number
    notification_type?: string
    channel?: string
    status?: string
  }): Promise<NotificationHistory[]> {
    return request.get('/v1/notifications/history', { params })
  },

  // 发送测试通知
  sendTestNotification(data: TestNotificationRequest): Promise<{ message: string }> {
    return request.post('/v1/notifications/test', data)
  },

  // 获取可用的通知渠道
  getAvailableChannels(): Promise<{
    channels: NotificationChannel[]
    total: number
  }> {
    return request.get('/v1/notifications/channels')
  },

  // 获取通知统计
  getStats(params?: {
    days?: number
  }): Promise<NotificationStats> {
    return request.get('/v1/notifications/stats', { params })
  }
}
