"""认证API"""
from datetime import timed<PERSON><PERSON>
from typing import Dict, Any

from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import HTT<PERSON><PERSON>earer
from pydantic import BaseModel, EmailStr, validator
from sqlalchemy.ext.asyncio import AsyncSession

from ..core.auth import auth_service, get_current_user
from ..models import User
from ..database import get_db_session
from ..utils import get_logger

logger = get_logger("auth_api")

router = APIRouter(prefix="/api/v1/auth", tags=["认证"])
security = HTTPBearer()


class RegisterRequest(BaseModel):
    """注册请求"""
    username: str
    email: EmailStr
    password: str
    
    @validator('username')
    def validate_username(cls, v):
        if len(v) < 3 or len(v) > 20:
            raise ValueError('用户名长度必须在3-20个字符之间')
        if not v.replace('_', '').replace('-', '').isalnum():
            raise ValueError('用户名只能包含字母、数字、下划线和连字符')
        return v
    
    @validator('password')
    def validate_password(cls, v):
        if len(v) < 6:
            raise ValueError('密码长度不能少于6个字符')
        return v


class LoginRequest(BaseModel):
    """登录请求"""
    username: str
    password: str


class TokenResponse(BaseModel):
    """令牌响应"""
    access_token: str
    token_type: str = "bearer"
    expires_in: int
    user: Dict[str, Any]


class UserResponse(BaseModel):
    """用户响应"""
    id: str
    username: str
    email: str
    is_active: bool
    is_verified: bool
    created_at: str
    last_login: str = None


class ChangePasswordRequest(BaseModel):
    """修改密码请求"""
    old_password: str
    new_password: str
    
    @validator('new_password')
    def validate_new_password(cls, v):
        if len(v) < 6:
            raise ValueError('新密码长度不能少于6个字符')
        return v


@router.post("/register", response_model=TokenResponse, summary="用户注册")
async def register(
    request: RegisterRequest,
    db: AsyncSession = Depends(get_db_session)
):
    """
    用户注册
    
    - **username**: 用户名（3-20个字符，只能包含字母、数字、下划线和连字符）
    - **email**: 邮箱地址
    - **password**: 密码（至少6个字符）
    """
    try:
        # 创建用户
        user = await auth_service.create_user(
            db=db,
            username=request.username,
            email=request.email,
            password=request.password
        )
        
        # 生成访问令牌
        access_token = auth_service.create_access_token(
            data={"sub": str(user.id)}
        )
        
        logger.info(f"用户注册成功: {request.username}")
        
        return TokenResponse(
            access_token=access_token,
            expires_in=auth_service.access_token_expire_minutes * 60,
            user=user.to_dict()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"用户注册失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="注册失败，请稍后重试"
        )


@router.post("/login", response_model=TokenResponse, summary="用户登录")
async def login(
    request: LoginRequest,
    db: AsyncSession = Depends(get_db_session)
):
    """
    用户登录
    
    - **username**: 用户名或邮箱
    - **password**: 密码
    """
    try:
        # 认证用户
        user = await auth_service.authenticate_user(
            db=db,
            username=request.username,
            password=request.password
        )
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码错误"
            )
        
        # 生成访问令牌
        access_token = auth_service.create_access_token(
            data={"sub": str(user.id)}
        )
        
        logger.info(f"用户登录成功: {request.username}")
        
        return TokenResponse(
            access_token=access_token,
            expires_in=auth_service.access_token_expire_minutes * 60,
            user=user.to_dict()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"用户登录失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="登录失败，请稍后重试"
        )


@router.post("/logout", summary="用户登出")
async def logout():
    """
    用户登出
    
    注意：由于使用JWT令牌，登出主要在客户端完成（删除本地存储的令牌）
    """
    return {"message": "登出成功"}


@router.get("/me", response_model=UserResponse, summary="获取当前用户信息")
async def get_current_user_info(
    current_user: User = Depends(get_current_user)
):
    """获取当前用户信息"""
    return UserResponse(**current_user.to_dict())


@router.put("/password", summary="修改密码")
async def change_password(
    request: ChangePasswordRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
):
    """
    修改密码
    
    - **old_password**: 当前密码
    - **new_password**: 新密码（至少6个字符）
    """
    try:
        # 验证当前密码
        if not auth_service.verify_password(request.old_password, current_user.password_hash):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="当前密码错误"
            )
        
        # 更新密码
        current_user.password_hash = auth_service.hash_password(request.new_password)
        await db.commit()
        
        logger.info(f"用户修改密码成功: {current_user.username}")
        
        return {"message": "密码修改成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"修改密码失败: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="密码修改失败，请稍后重试"
        )


@router.get("/verify-token", summary="验证令牌")
async def verify_token(
    current_user: User = Depends(get_current_user)
):
    """验证令牌是否有效"""
    return {
        "valid": True,
        "user": current_user.to_dict()
    }
