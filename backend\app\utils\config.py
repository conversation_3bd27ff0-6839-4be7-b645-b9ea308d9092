"""配置管理模块"""
import os
import yaml
from typing import Dict, Any, Optional
from pathlib import Path
from pydantic import BaseSettings, Field


class DatabaseSettings(BaseSettings):
    """数据库配置"""
    url: str = Field(default="postgresql://postgres:password@localhost:5432/live_monitor")
    echo: bool = Field(default=True)
    pool_size: int = Field(default=5)
    max_overflow: int = Field(default=10)


class RedisSettings(BaseSettings):
    """Redis配置"""
    url: str = Field(default="redis://localhost:6379/0")
    max_connections: int = Field(default=10)


class SecuritySettings(BaseSettings):
    """安全配置"""
    secret_key: str = Field(default="your-secret-key-here")
    algorithm: str = Field(default="HS256")
    access_token_expire_minutes: int = Field(default=30)


class MonitoringSettings(BaseSettings):
    """监控配置"""
    check_interval: int = Field(default=30)
    max_concurrent_checks: int = Field(default=10)
    retry_attempts: int = Field(default=3)
    retry_delay: int = Field(default=5)


class NotificationSettings(BaseSettings):
    """通知配置"""
    wxpusher_app_token: str = Field(default="")
    wxpusher_base_url: str = Field(default="https://wxpusher.zjiecode.com/api")
    
    email_smtp_server: str = Field(default="smtp.gmail.com")
    email_smtp_port: int = Field(default=587)
    email_username: str = Field(default="")
    email_password: str = Field(default="")


class PlatformSettings(BaseSettings):
    """平台配置"""
    bilibili_base_url: str = Field(default="https://api.live.bilibili.com")
    bilibili_rate_limit: int = Field(default=60)
    
    douyu_base_url: str = Field(default="https://open.douyucdn.cn/api")
    douyu_rate_limit: int = Field(default=60)
    
    huya_base_url: str = Field(default="https://mp.huya.com")
    huya_rate_limit: int = Field(default=60)


class LoggingSettings(BaseSettings):
    """日志配置"""
    level: str = Field(default="DEBUG")
    format: str = Field(default="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
    file: str = Field(default="logs/app.log")
    max_bytes: int = Field(default=10485760)
    backup_count: int = Field(default=5)


class AppSettings(BaseSettings):
    """应用配置"""
    name: str = Field(default="直播监控系统")
    version: str = Field(default="1.0.0")
    debug: bool = Field(default=True)
    host: str = Field(default="0.0.0.0")
    port: int = Field(default=8000)
    
    # 子配置
    database: DatabaseSettings = Field(default_factory=DatabaseSettings)
    redis: RedisSettings = Field(default_factory=RedisSettings)
    security: SecuritySettings = Field(default_factory=SecuritySettings)
    monitoring: MonitoringSettings = Field(default_factory=MonitoringSettings)
    notifications: NotificationSettings = Field(default_factory=NotificationSettings)
    platforms: PlatformSettings = Field(default_factory=PlatformSettings)
    logging: LoggingSettings = Field(default_factory=LoggingSettings)

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_path: Optional[str] = None):
        self.config_path = config_path or self._get_default_config_path()
        self._settings: Optional[AppSettings] = None
    
    def _get_default_config_path(self) -> str:
        """获取默认配置文件路径"""
        env = os.getenv("ENVIRONMENT", "development")
        config_dir = Path(__file__).parent.parent.parent.parent / "config"
        return str(config_dir / f"{env}.yaml")
    
    def load_config(self) -> AppSettings:
        """加载配置"""
        if self._settings is None:
            # 先从环境变量加载基础配置
            self._settings = AppSettings()
            
            # 如果配置文件存在，则覆盖配置
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    yaml_config = yaml.safe_load(f)
                    self._merge_yaml_config(yaml_config)
        
        return self._settings
    
    def _merge_yaml_config(self, yaml_config: Dict[str, Any]) -> None:
        """合并YAML配置"""
        if not yaml_config:
            return
        
        # 更新数据库配置
        if "database" in yaml_config:
            db_config = yaml_config["database"]
            self._settings.database = DatabaseSettings(**db_config)
        
        # 更新Redis配置
        if "redis" in yaml_config:
            redis_config = yaml_config["redis"]
            self._settings.redis = RedisSettings(**redis_config)
        
        # 更新安全配置
        if "security" in yaml_config:
            security_config = yaml_config["security"]
            self._settings.security = SecuritySettings(**security_config)
        
        # 更新监控配置
        if "monitoring" in yaml_config:
            monitoring_config = yaml_config["monitoring"]
            self._settings.monitoring = MonitoringSettings(**monitoring_config)
        
        # 更新通知配置
        if "notifications" in yaml_config:
            notifications_config = yaml_config["notifications"]
            # 展平嵌套配置
            flat_config = {}
            if "wxpusher" in notifications_config:
                flat_config["wxpusher_app_token"] = notifications_config["wxpusher"].get("app_token", "")
                flat_config["wxpusher_base_url"] = notifications_config["wxpusher"].get("base_url", "")
            if "email" in notifications_config:
                flat_config["email_smtp_server"] = notifications_config["email"].get("smtp_server", "")
                flat_config["email_smtp_port"] = notifications_config["email"].get("smtp_port", 587)
                flat_config["email_username"] = notifications_config["email"].get("username", "")
                flat_config["email_password"] = notifications_config["email"].get("password", "")
            
            self._settings.notifications = NotificationSettings(**flat_config)
        
        # 更新平台配置
        if "platforms" in yaml_config:
            platforms_config = yaml_config["platforms"]
            flat_config = {}
            if "bilibili" in platforms_config:
                flat_config["bilibili_base_url"] = platforms_config["bilibili"].get("base_url", "")
                flat_config["bilibili_rate_limit"] = platforms_config["bilibili"].get("rate_limit", 60)
            if "douyu" in platforms_config:
                flat_config["douyu_base_url"] = platforms_config["douyu"].get("base_url", "")
                flat_config["douyu_rate_limit"] = platforms_config["douyu"].get("rate_limit", 60)
            if "huya" in platforms_config:
                flat_config["huya_base_url"] = platforms_config["huya"].get("base_url", "")
                flat_config["huya_rate_limit"] = platforms_config["huya"].get("rate_limit", 60)
            
            self._settings.platforms = PlatformSettings(**flat_config)
        
        # 更新日志配置
        if "logging" in yaml_config:
            logging_config = yaml_config["logging"]
            self._settings.logging = LoggingSettings(**logging_config)
        
        # 更新应用配置
        if "app" in yaml_config:
            app_config = yaml_config["app"]
            for key, value in app_config.items():
                if hasattr(self._settings, key):
                    setattr(self._settings, key, value)


# 全局配置管理器实例
config_manager = ConfigManager()

def get_settings() -> AppSettings:
    """获取应用配置"""
    return config_manager.load_config()
