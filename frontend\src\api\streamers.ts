import { request } from './index'

// 主播接口
export interface Streamer {
  id: string
  platform: string
  streamer_id: string
  streamer_name: string
  is_live: boolean
  title: string
  viewer_count: number
  live_url: string
  cover_image: string
  avatar_url: string
  last_updated: string
  subscription_id: string
  is_subscribed: boolean
}

// 搜索主播结果
export interface SearchStreamerResult {
  platform: string
  streamer_id: string
  streamer_name: string
  title: string
  is_live: boolean
  viewer_count: number
  cover_image: string
  avatar_url: string
  live_url: string
  is_subscribed: boolean
}

// 添加主播请求
export interface AddStreamerRequest {
  platform: string
  streamer_id: string
  notification_preferences?: {
    notify_live_start?: boolean
    notify_live_end?: boolean
    notify_title_change?: boolean
    priority?: string
  }
}

// 订阅设置
export interface SubscriptionSettings {
  notify_live_start: boolean
  notify_live_end: boolean
  notify_title_change: boolean
  priority: 'low' | 'normal' | 'high'
}

// 主播管理API
export const streamersApi = {
  // 获取用户订阅的主播列表
  getStreamers(params?: {
    platform?: string
    is_live?: boolean
  }): Promise<Streamer[]> {
    return request.get('/v1/monitors/streamers', { params })
  },

  // 添加主播订阅
  addStreamer(data: AddStreamerRequest): Promise<{
    message: string
    streamer: Streamer
    subscription_id: string
  }> {
    return request.post('/v1/monitors/streamers', data)
  },

  // 取消主播订阅
  removeStreamer(streamerId: string): Promise<{ message: string }> {
    return request.delete(`/v1/monitors/streamers/${streamerId}`)
  },

  // 更新主播通知设置
  updateStreamerSettings(
    streamerId: string,
    settings: SubscriptionSettings
  ): Promise<{ message: string }> {
    return request.put(`/v1/monitors/streamers/${streamerId}/settings`, settings)
  },

  // 搜索主播
  searchStreamers(params: {
    platform: string
    keyword: string
  }): Promise<SearchStreamerResult[]> {
    return request.get('/v1/monitors/search', { params })
  }
}
