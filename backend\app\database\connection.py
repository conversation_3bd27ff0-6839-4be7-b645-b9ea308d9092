"""数据库连接模块"""
from typing import AsyncGenerator
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.orm import declarative_base

from ..utils import get_settings, get_logger

logger = get_logger("database")

# 创建基础模型类
Base = declarative_base()

# 全局变量
engine = None
async_session_maker = None


async def init_database() -> None:
    """初始化数据库连接"""
    global engine, async_session_maker
    
    settings = get_settings()
    
    # 将PostgreSQL URL转换为异步版本
    database_url = settings.database.url
    if database_url.startswith("postgresql://"):
        database_url = database_url.replace("postgresql://", "postgresql+asyncpg://", 1)
    
    logger.info(f"连接数据库: {database_url.split('@')[1] if '@' in database_url else database_url}")
    
    # 创建异步引擎
    engine = create_async_engine(
        database_url,
        echo=settings.database.echo,
        pool_size=settings.database.pool_size,
        max_overflow=settings.database.max_overflow,
        pool_pre_ping=True,  # 连接前检查
        pool_recycle=3600,   # 1小时回收连接
    )
    
    # 创建会话工厂
    async_session_maker = async_sessionmaker(
        engine,
        class_=AsyncSession,
        expire_on_commit=False
    )
    
    logger.info("数据库连接初始化完成")


async def close_database() -> None:
    """关闭数据库连接"""
    global engine
    
    if engine:
        await engine.dispose()
        logger.info("数据库连接已关闭")


async def get_db_session() -> AsyncGenerator[AsyncSession, None]:
    """获取数据库会话
    
    Yields:
        数据库会话
    """
    if not async_session_maker:
        await init_database()
    
    async with async_session_maker() as session:
        try:
            yield session
        except Exception as e:
            await session.rollback()
            logger.error(f"数据库会话异常: {e}")
            raise
        finally:
            await session.close()


async def create_tables() -> None:
    """创建数据库表"""
    if not engine:
        await init_database()
    
    # 导入所有模型以确保它们被注册
    from ..models import user, streamer, subscription, notification
    
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    logger.info("数据库表创建完成")


async def drop_tables() -> None:
    """删除数据库表"""
    if not engine:
        await init_database()
    
    # 导入所有模型
    from ..models import user, streamer, subscription, notification
    
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)
    
    logger.info("数据库表删除完成")


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        self.engine = None
        self.session_maker = None
    
    async def initialize(self) -> None:
        """初始化数据库"""
        await init_database()
        self.engine = engine
        self.session_maker = async_session_maker
    
    async def close(self) -> None:
        """关闭数据库连接"""
        await close_database()
    
    async def get_session(self) -> AsyncGenerator[AsyncSession, None]:
        """获取数据库会话"""
        async for session in get_db_session():
            yield session
    
    async def create_all_tables(self) -> None:
        """创建所有表"""
        await create_tables()
    
    async def drop_all_tables(self) -> None:
        """删除所有表"""
        await drop_tables()


# 全局数据库管理器实例
db_manager = DatabaseManager()
