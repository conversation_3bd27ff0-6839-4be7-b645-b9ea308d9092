"""监控管理API"""
from typing import List, Optional, Dict, Any
import uuid

from fastapi import APIRouter, Depends, HTTPException, status, Query
from pydantic import BaseModel, validator
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_

from ..core.auth import get_current_user
from ..models import User, Streamer, Subscription
from ..platforms import platform_manager
from ..database import get_db_session
from ..utils import get_logger

logger = get_logger("monitors_api")

router = APIRouter(prefix="/api/v1/monitors", tags=["监控管理"])


class AddStreamerRequest(BaseModel):
    """添加主播请求"""
    platform: str
    streamer_id: str
    notification_preferences: Optional[Dict[str, Any]] = None
    
    @validator('platform')
    def validate_platform(cls, v):
        if v not in ['bilibili', 'douyu', 'huya']:
            raise ValueError('不支持的平台')
        return v
    
    @validator('streamer_id')
    def validate_streamer_id(cls, v):
        if not v.strip():
            raise ValueError('主播ID不能为空')
        return v.strip()


class StreamerResponse(BaseModel):
    """主播响应"""
    id: str
    platform: str
    streamer_id: str
    streamer_name: str
    is_live: bool
    title: str = ""
    viewer_count: int = 0
    live_url: str
    cover_image: str = ""
    avatar_url: str = ""
    last_updated: str = ""
    subscription_id: str = ""
    is_subscribed: bool = True


class SearchStreamerResponse(BaseModel):
    """搜索主播响应"""
    platform: str
    streamer_id: str
    streamer_name: str
    title: str = ""
    is_live: bool = False
    viewer_count: int = 0
    cover_image: str = ""
    avatar_url: str = ""
    live_url: str = ""
    is_subscribed: bool = False


class SubscriptionSettingsRequest(BaseModel):
    """订阅设置请求"""
    notify_live_start: bool = True
    notify_live_end: bool = True
    notify_title_change: bool = True
    priority: str = "normal"
    
    @validator('priority')
    def validate_priority(cls, v):
        if v not in ['low', 'normal', 'high']:
            raise ValueError('优先级必须是 low、normal 或 high')
        return v


@router.post("/streamers", summary="添加主播监控")
async def add_streamer(
    request: AddStreamerRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
):
    """
    添加主播监控
    
    - **platform**: 平台名称（bilibili、douyu、huya）
    - **streamer_id**: 主播ID
    - **notification_preferences**: 通知偏好设置（可选）
    """
    try:
        # 验证平台是否支持
        if not platform_manager.is_platform_supported(request.platform):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"不支持的平台: {request.platform}"
            )
        
        # 验证主播是否存在
        platform = platform_manager.get_platform(request.platform)
        if not platform:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="平台服务不可用"
            )
        
        # 获取主播信息
        try:
            streamer_status = await platform.get_streamer_status(request.streamer_id)
        except Exception as e:
            logger.error(f"获取主播信息失败: {request.platform}:{request.streamer_id}, 错误: {e}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="主播不存在或获取信息失败"
            )
        
        # 查找或创建主播记录
        stmt = select(Streamer).where(
            and_(
                Streamer.platform == request.platform,
                Streamer.streamer_id == request.streamer_id
            )
        )
        result = await db.execute(stmt)
        streamer = result.scalar_one_or_none()
        
        if not streamer:
            # 创建新的主播记录
            streamer = Streamer(
                platform=request.platform,
                streamer_id=request.streamer_id,
                streamer_name=streamer_status.get('streamer_name', ''),
                avatar_url=streamer_status.get('avatar_url', ''),
                profile_url=streamer_status.get('profile_url', '')
            )
            streamer.set_last_status(streamer_status)
            db.add(streamer)
            await db.flush()  # 获取ID
        else:
            # 更新现有主播信息
            streamer.streamer_name = streamer_status.get('streamer_name', streamer.streamer_name)
            streamer.avatar_url = streamer_status.get('avatar_url', streamer.avatar_url)
            streamer.set_last_status(streamer_status)
        
        # 检查是否已经订阅
        stmt = select(Subscription).where(
            and_(
                Subscription.user_id == current_user.id,
                Subscription.streamer_id == streamer.id
            )
        )
        result = await db.execute(stmt)
        existing_subscription = result.scalar_one_or_none()
        
        if existing_subscription:
            if existing_subscription.is_active:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="已经订阅了该主播"
                )
            else:
                # 重新激活订阅
                existing_subscription.activate()
                if request.notification_preferences:
                    existing_subscription.set_custom_settings(request.notification_preferences)
                subscription = existing_subscription
        else:
            # 创建新订阅
            subscription = Subscription(
                user_id=current_user.id,
                streamer_id=streamer.id
            )
            if request.notification_preferences:
                subscription.set_custom_settings(request.notification_preferences)
            db.add(subscription)
        
        await db.commit()
        
        logger.info(f"用户 {current_user.username} 添加主播订阅: {request.platform}:{request.streamer_id}")
        
        return {
            "message": "主播订阅添加成功",
            "streamer": streamer.to_status_dict(),
            "subscription_id": str(subscription.id)
        }
        
    except HTTPException:
        await db.rollback()
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"添加主播订阅失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="添加订阅失败，请稍后重试"
        )


@router.get("/streamers", response_model=List[StreamerResponse], summary="获取用户订阅的主播列表")
async def get_user_streamers(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session),
    platform: Optional[str] = Query(None, description="平台筛选"),
    is_live: Optional[bool] = Query(None, description="直播状态筛选")
):
    """
    获取用户订阅的主播列表
    
    - **platform**: 平台筛选（可选）
    - **is_live**: 直播状态筛选（可选）
    """
    try:
        # 构建查询
        stmt = select(
            Subscription.id.label('subscription_id'),
            Streamer
        ).select_from(
            Subscription.__table__.join(Streamer.__table__)
        ).where(
            and_(
                Subscription.user_id == current_user.id,
                Subscription.is_active == True
            )
        )
        
        # 应用平台筛选
        if platform:
            stmt = stmt.where(Streamer.platform == platform)
        
        result = await db.execute(stmt)
        rows = result.fetchall()
        
        streamers = []
        for row in rows:
            streamer_dict = row.Streamer.to_status_dict()
            streamer_dict['subscription_id'] = str(row.subscription_id)
            
            # 应用直播状态筛选
            if is_live is not None:
                if streamer_dict['is_live'] != is_live:
                    continue
            
            streamers.append(StreamerResponse(**streamer_dict))
        
        return streamers
        
    except Exception as e:
        logger.error(f"获取用户订阅列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取订阅列表失败"
        )


@router.delete("/streamers/{streamer_id}", summary="取消主播订阅")
async def remove_streamer(
    streamer_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
):
    """取消主播订阅"""
    try:
        # 验证UUID格式
        try:
            uuid.UUID(streamer_id)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的主播ID格式"
            )
        
        # 查找订阅
        stmt = select(Subscription).where(
            and_(
                Subscription.user_id == current_user.id,
                Subscription.streamer_id == streamer_id,
                Subscription.is_active == True
            )
        )
        result = await db.execute(stmt)
        subscription = result.scalar_one_or_none()
        
        if not subscription:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="订阅不存在"
            )
        
        # 停用订阅
        subscription.deactivate()
        await db.commit()
        
        logger.info(f"用户 {current_user.username} 取消主播订阅: {streamer_id}")
        
        return {"message": "取消订阅成功"}
        
    except HTTPException:
        await db.rollback()
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"取消订阅失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="取消订阅失败，请稍后重试"
        )


@router.put("/streamers/{streamer_id}/settings", summary="更新主播通知设置")
async def update_streamer_settings(
    streamer_id: str,
    settings: SubscriptionSettingsRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
):
    """更新主播通知设置"""
    try:
        # 验证UUID格式
        try:
            uuid.UUID(streamer_id)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的主播ID格式"
            )
        
        # 查找订阅
        stmt = select(Subscription).where(
            and_(
                Subscription.user_id == current_user.id,
                Subscription.streamer_id == streamer_id,
                Subscription.is_active == True
            )
        )
        result = await db.execute(stmt)
        subscription = result.scalar_one_or_none()
        
        if not subscription:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="订阅不存在"
            )
        
        # 更新设置
        subscription.set_custom_settings(settings.dict())
        await db.commit()
        
        logger.info(f"用户 {current_user.username} 更新主播通知设置: {streamer_id}")
        
        return {"message": "设置更新成功"}
        
    except HTTPException:
        await db.rollback()
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"更新通知设置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="设置更新失败，请稍后重试"
        )


@router.get("/search", response_model=List[SearchStreamerResponse], summary="搜索主播")
async def search_streamers(
    platform: str = Query(..., description="平台名称"),
    keyword: str = Query(..., description="搜索关键词"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
):
    """
    搜索主播
    
    - **platform**: 平台名称（bilibili、douyu、huya）
    - **keyword**: 搜索关键词
    """
    try:
        # 验证平台
        if not platform_manager.is_platform_supported(platform):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"不支持的平台: {platform}"
            )
        
        # 搜索主播
        search_results = await platform_manager.search_streamers(platform, keyword)
        
        # 获取用户已订阅的主播
        stmt = select(Streamer.streamer_id).select_from(
            Subscription.__table__.join(Streamer.__table__)
        ).where(
            and_(
                Subscription.user_id == current_user.id,
                Subscription.is_active == True,
                Streamer.platform == platform
            )
        )
        result = await db.execute(stmt)
        subscribed_ids = {row[0] for row in result.fetchall()}
        
        # 构造响应
        responses = []
        for result in search_results:
            response = SearchStreamerResponse(
                **result,
                is_subscribed=result['streamer_id'] in subscribed_ids
            )
            responses.append(response)
        
        return responses
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"搜索主播失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="搜索失败，请稍后重试"
        )
