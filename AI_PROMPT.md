# 🎯 直播监控系统开发提示词

## 📋 项目需求概述

开发一个现代化的直播监控系统，用于实时监控多个直播平台（B站、斗鱼、虎牙等）的主播状态，并通过多种渠道向用户发送通知。系统需要具备高性能、高可用、易扩展的特点。

### 🎯 核心功能需求

#### 1. 用户管理系统
- **用户注册/登录**：支持邮箱注册、密码登录
- **用户认证**：JWT令牌认证机制
- **订阅管理**：用户可以订阅/取消订阅主播
- **通知偏好**：自定义通知设置（开播、下播、标题变更）
- **免打扰时段**：设置静默时间避免深夜打扰

#### 2. 多平台监控
- **平台支持**：B站、斗鱼、虎牙（可扩展更多平台）
- **实时监控**：WebSocket实时推送 + 轮询备选方案
- **状态检测**：开播、下播、标题变更检测
- **智能调度**：自适应监控频率，避免API限流
- **容错处理**：网络异常、API失败的重试机制
- **混合监控**：WebSocket优先，轮询备选，确保监控稳定性

#### 3. 通知系统
- **多渠道支持**：微信推送（WxPusher）、邮件、短信、QQ机器人
- **通知模板**：可自定义的消息模板
- **发送策略**：防重复发送、批量发送优化
- **历史记录**：通知发送历史和状态追踪

#### 4. Web管理界面
- **用户仪表板**：显示订阅状态、实时数据
- **订阅管理**：添加/删除/配置主播订阅
- **通知设置**：管理通知偏好和渠道
- **数据统计**：监控数据可视化展示

#### 5. 系统管理
- **配置管理**：灵活的配置文件系统
- **日志系统**：结构化日志记录和分析
- **监控告警**：系统健康状态监控
- **性能优化**：缓存、批处理、异步处理

### 🏗️ 技术架构要求

#### 后端技术栈
- **编程语言**：Python 3.8+
- **Web框架**：FastAPI（高性能、自动文档生成）
- **异步处理**：asyncio + aiohttp + websockets
- **实时通信**：WebSocket + Server-Sent Events (SSE)
- **数据库**：PostgreSQL（主数据库）+ Redis（缓存）
- **消息队列**：Redis Streams 或 RabbitMQ
- **认证授权**：JWT + OAuth2

#### 前端技术栈
- **框架选择**：Vue 3 + TypeScript 或 React + TypeScript
- **UI组件库**：Element Plus 或 Ant Design
- **状态管理**：Pinia 或 Redux Toolkit
- **构建工具**：Vite 或 Webpack
- **样式方案**：Tailwind CSS 或 Styled Components

#### 基础设施
- **容器化**：Docker + Docker Compose
- **部署方案**：支持云服务器、VPS部署
- **监控工具**：Prometheus + Grafana（可选）
- **日志收集**：结构化日志 + 文件轮转

## 🏗️ 系统架构设计

### 🎨 架构设计原则
1. **模块化设计**：清晰的模块边界和职责分离
2. **异步优先**：充分利用Python异步特性
3. **可扩展性**：支持新平台和通知渠道的快速接入
4. **高可用性**：容错处理和自动恢复机制
5. **配置驱动**：通过配置文件控制系统行为
6. **测试友好**：便于单元测试和集成测试

### 📦 推荐项目结构

```
live_monitor/
├── backend/                    # 后端服务
│   ├── app/                   # 主应用目录
│   │   ├── api/              # API路由
│   │   │   ├── __init__.py
│   │   │   ├── auth.py       # 认证相关API
│   │   │   ├── users.py      # 用户管理API
│   │   │   ├── monitors.py   # 监控管理API
│   │   │   ├── notifications.py # 通知管理API
│   │   │   └── analytics.py  # 数据分析API
│   │   ├── core/             # 核心业务逻辑
│   │   │   ├── __init__.py
│   │   │   ├── auth.py       # 认证服务
│   │   │   ├── monitor.py    # 监控服务
│   │   │   ├── notification.py # 通知服务
│   │   │   └── scheduler.py  # 任务调度器
│   │   ├── models/           # 数据模型
│   │   │   ├── __init__.py
│   │   │   ├── user.py       # 用户模型
│   │   │   ├── streamer.py   # 主播模型
│   │   │   ├── subscription.py # 订阅模型
│   │   │   └── notification.py # 通知模型
│   │   ├── platforms/        # 平台适配器
│   │   │   ├── __init__.py
│   │   │   ├── base.py       # 基础平台类
│   │   │   ├── bilibili.py   # B站适配器
│   │   │   ├── douyu.py      # 斗鱼适配器
│   │   │   └── huya.py       # 虎牙适配器
│   │   ├── notifications/    # 通知渠道
│   │   │   ├── __init__.py
│   │   │   ├── base.py       # 基础通知类
│   │   │   ├── wxpusher.py   # 微信推送
│   │   │   ├── email.py      # 邮件通知
│   │   │   └── qq_bot.py     # QQ机器人
│   │   ├── utils/            # 工具模块
│   │   │   ├── __init__.py
│   │   │   ├── config.py     # 配置管理
│   │   │   ├── logger.py     # 日志工具
│   │   │   ├── cache.py      # 缓存工具
│   │   │   ├── http_client.py # HTTP客户端
│   │   │   └── helpers.py    # 辅助函数
│   │   ├── database/         # 数据库相关
│   │   │   ├── __init__.py
│   │   │   ├── connection.py # 数据库连接
│   │   │   ├── migrations/   # 数据库迁移
│   │   │   └── seeds/        # 初始数据
│   │   └── main.py           # 应用入口
│   ├── tests/                # 测试代码
│   │   ├── __init__.py
│   │   ├── conftest.py       # 测试配置
│   │   ├── test_api/         # API测试
│   │   ├── test_core/        # 核心逻辑测试
│   │   └── test_platforms/   # 平台适配器测试
│   ├── requirements.txt      # Python依赖
│   ├── Dockerfile           # Docker配置
│   └── pyproject.toml       # 项目配置
├── frontend/                 # 前端应用
│   ├── src/                 # 源代码
│   │   ├── components/      # 组件
│   │   ├── views/           # 页面
│   │   ├── stores/          # 状态管理
│   │   ├── api/             # API调用
│   │   ├── utils/           # 工具函数
│   │   └── main.ts          # 入口文件
│   ├── public/              # 静态资源
│   ├── package.json         # 依赖配置
│   ├── vite.config.ts       # 构建配置
│   └── Dockerfile           # Docker配置
├── config/                   # 配置文件
│   ├── development.yaml     # 开发环境配置
│   ├── production.yaml      # 生产环境配置
│   └── docker-compose.yml   # Docker Compose配置
├── docs/                     # 文档
│   ├── api.md               # API文档
│   ├── deployment.md        # 部署文档
│   └── development.md       # 开发文档
├── scripts/                  # 脚本工具
│   ├── build.sh             # 构建脚本
│   ├── deploy.sh            # 部署脚本
│   └── init_db.py           # 数据库初始化
├── .env.example             # 环境变量示例
├── docker-compose.yml       # Docker Compose配置
├── Makefile                 # 构建工具
└── README.md                # 项目说明
```

## 🔧 核心模块设计

### 1. 用户认证模块 (app/core/auth.py)
```python
from datetime import datetime, timedelta
from typing import Optional
import jwt
from passlib.context import CryptContext
from fastapi import HTTPException, status

class AuthService:
    def __init__(self, secret_key: str, algorithm: str = "HS256"):
        self.secret_key = secret_key
        self.algorithm = algorithm
        self.pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
    
    def create_access_token(self, data: dict, expires_delta: Optional[timedelta] = None):
        """创建JWT访问令牌"""
        to_encode = data.copy()
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=15)
        
        to_encode.update({"exp": expire})
        encoded_jwt = jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
        return encoded_jwt
    
    def verify_token(self, token: str) -> dict:
        """验证JWT令牌"""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            return payload
        except jwt.PyJWTError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的认证令牌"
            )
    
    def hash_password(self, password: str) -> str:
        """密码哈希"""
        return self.pwd_context.hash(password)
    
    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """验证密码"""
        return self.pwd_context.verify(plain_password, hashed_password)
```

### 2. 监控服务模块 (app/core/monitor.py)
```python
import asyncio
from typing import List, Dict, Any
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

class MonitorService:
    def __init__(self, platforms: Dict, notification_service, cache_service):
        self.platforms = platforms
        self.notification_service = notification_service
        self.cache_service = cache_service
        self.running = False
        self.check_interval = 30  # 默认30秒检查一次
    
    async def start_monitoring(self):
        """启动监控服务"""
        self.running = True
        logger.info("监控服务已启动")
        
        while self.running:
            try:
                await self._check_all_streamers()
                await asyncio.sleep(self.check_interval)
            except Exception as e:
                logger.error(f"监控过程出错: {e}")
                await asyncio.sleep(5)  # 出错后短暂等待
    
    async def stop_monitoring(self):
        """停止监控服务"""
        self.running = False
        logger.info("监控服务已停止")
    
    async def _check_all_streamers(self):
        """检查所有主播状态"""
        # 获取所有活跃的订阅
        subscriptions = await self._get_active_subscriptions()
        
        # 按平台分组，批量检查
        platform_groups = self._group_by_platform(subscriptions)
        
        tasks = []
        for platform_name, streamers in platform_groups.items():
            task = self._check_platform_streamers(platform_name, streamers)
            tasks.append(task)
        
        # 并发执行所有平台的检查
        await asyncio.gather(*tasks, return_exceptions=True)
    
    async def _check_platform_streamers(self, platform_name: str, streamers: List[Dict]):
        """检查特定平台的主播"""
        platform = self.platforms.get(platform_name)
        if not platform:
            logger.warning(f"未找到平台适配器: {platform_name}")
            return
        
        for streamer in streamers:
            try:
                # 获取当前状态
                current_status = await platform.get_streamer_status(streamer['streamer_id'])
                
                # 获取缓存的历史状态
                cache_key = f"streamer:{platform_name}:{streamer['streamer_id']}"
                previous_status = await self.cache_service.get(cache_key)
                
                # 检测状态变化
                if self._status_changed(previous_status, current_status):
                    # 发送通知
                    await self._send_notifications(streamer, previous_status, current_status)
                
                # 更新缓存
                await self.cache_service.set(cache_key, current_status, ttl=3600)
                
            except Exception as e:
                logger.error(f"检查主播 {streamer['streamer_id']} 失败: {e}")
    
    def _status_changed(self, old_status: Dict, new_status: Dict) -> bool:
        """检测状态是否发生变化"""
        if not old_status:
            return True  # 首次检查
        
        # 检查直播状态变化
        if old_status.get('is_live') != new_status.get('is_live'):
            return True
        
        # 检查标题变化
        if old_status.get('title') != new_status.get('title'):
            return True
        
        return False
    
    async def _send_notifications(self, streamer: Dict, old_status: Dict, new_status: Dict):
        """发送通知"""
        # 获取订阅该主播的用户
        subscribers = await self._get_streamer_subscribers(streamer['id'])
        
        for subscriber in subscribers:
            # 根据用户偏好发送通知
            await self.notification_service.send_streamer_notification(
                user_id=subscriber['user_id'],
                streamer=streamer,
                old_status=old_status,
                new_status=new_status,
                preferences=subscriber['notification_preferences']
            )

## 🔄 WebSocket实时监控系统

### 实时监控架构设计

#### 1. 混合监控策略
```python
class HybridMonitorService:
    """混合监控服务：WebSocket优先，轮询备选"""

    def __init__(self):
        self.websocket_handlers = {
            'bilibili': BilibiliWebSocketHandler(),
            'douyu': DouyuWebSocketHandler(),
            'huya': HuyaWebSocketHandler(),
        }
        self.polling_monitor = PollingMonitor()
        self.connection_status = {}

    async def start_monitoring(self, subscriptions):
        """启动混合监控"""
        tasks = []

        for subscription in subscriptions:
            platform = subscription['platform']

            # 优先尝试WebSocket
            if platform in self.websocket_handlers:
                task = self._start_websocket_monitor(subscription)
            else:
                task = self._start_polling_monitor(subscription)

            tasks.append(task)

        await asyncio.gather(*tasks, return_exceptions=True)

    async def _start_websocket_monitor(self, subscription):
        """启动WebSocket监控"""
        platform = subscription['platform']
        handler = self.websocket_handlers[platform]

        try:
            await handler.monitor_streamer(subscription)
        except Exception as e:
            logger.warning(f"WebSocket监控失败，切换到轮询: {e}")
            await self._start_polling_monitor(subscription)

    async def _start_polling_monitor(self, subscription):
        """启动轮询监控（备选方案）"""
        await self.polling_monitor.monitor_streamer(subscription)

#### 2. B站WebSocket实现
```python
import websockets
import json
import struct
import asyncio
from typing import Dict, Any, Optional

class BilibiliWebSocketHandler:
    """B站WebSocket处理器"""

    def __init__(self):
        self.ws_url = "wss://broadcastlv.chat.bilibili.com:2245/sub"
        self.heartbeat_interval = 30

    async def monitor_streamer(self, subscription):
        """监控B站主播"""
        room_id = subscription['streamer_id']

        while True:
            try:
                async with websockets.connect(self.ws_url) as websocket:
                    # 发送认证包
                    auth_packet = self._create_auth_packet(room_id)
                    await websocket.send(auth_packet)

                    # 启动心跳任务
                    heartbeat_task = asyncio.create_task(
                        self._send_heartbeat(websocket)
                    )

                    # 监听消息
                    try:
                        async for message in websocket:
                            await self._handle_message(message, subscription)
                    finally:
                        heartbeat_task.cancel()

            except Exception as e:
                logger.error(f"B站WebSocket连接失败: {e}")
                await asyncio.sleep(30)  # 重连等待

    def _create_auth_packet(self, room_id: str) -> bytes:
        """创建认证数据包"""
        # B站WebSocket协议实现
        auth_data = {
            "uid": 0,
            "roomid": int(room_id),
            "protover": 2,
            "platform": "web",
            "clientver": "1.14.3",
            "type": 2,
            "key": ""  # 可以为空
        }

        auth_json = json.dumps(auth_data).encode('utf-8')
        packet_length = len(auth_json) + 16

        # 构造数据包头部
        header = struct.pack('>I', packet_length)  # 包长度
        header += struct.pack('>H', 16)            # 头部长度
        header += struct.pack('>H', 1)             # 协议版本
        header += struct.pack('>I', 7)             # 操作码（认证）
        header += struct.pack('>I', 1)             # sequence

        return header + auth_json

    async def _send_heartbeat(self, websocket):
        """发送心跳包"""
        while True:
            try:
                await asyncio.sleep(self.heartbeat_interval)
                heartbeat_packet = self._create_heartbeat_packet()
                await websocket.send(heartbeat_packet)
            except Exception as e:
                logger.error(f"心跳发送失败: {e}")
                break

    def _create_heartbeat_packet(self) -> bytes:
        """创建心跳数据包"""
        packet_length = 16
        header = struct.pack('>I', packet_length)  # 包长度
        header += struct.pack('>H', 16)            # 头部长度
        header += struct.pack('>H', 1)             # 协议版本
        header += struct.pack('>I', 2)             # 操作码（心跳）
        header += struct.pack('>I', 1)             # sequence
        return header

    async def _handle_message(self, message: bytes, subscription: Dict):
        """处理WebSocket消息"""
        try:
            # 解析消息头部
            if len(message) < 16:
                return

            packet_length = struct.unpack('>I', message[:4])[0]
            header_length = struct.unpack('>H', message[4:6])[0]
            protocol_version = struct.unpack('>H', message[6:8])[0]
            operation = struct.unpack('>I', message[8:12])[0]
            sequence = struct.unpack('>I', message[12:16])[0]

            # 提取消息体
            if len(message) > header_length:
                body = message[header_length:]

                if operation == 5:  # 消息操作码
                    await self._parse_live_message(body, subscription)

        except Exception as e:
            logger.error(f"消息解析失败: {e}")

    async def _parse_live_message(self, body: bytes, subscription: Dict):
        """解析直播消息"""
        try:
            # 根据协议版本解析消息
            message_text = body.decode('utf-8')
            data = json.loads(message_text)

            cmd = data.get('cmd', '')

            if cmd == 'LIVE':
                # 开播消息
                await self._handle_live_start(data, subscription)
            elif cmd == 'PREPARING':
                # 下播消息
                await self._handle_live_end(data, subscription)
            elif cmd == 'ROOM_CHANGE':
                # 房间信息变更（标题等）
                await self._handle_room_change(data, subscription)

        except Exception as e:
            logger.error(f"直播消息解析失败: {e}")

    async def _handle_live_start(self, data: Dict, subscription: Dict):
        """处理开播事件"""
        event_data = {
            'type': 'live_start',
            'platform': 'bilibili',
            'streamer_id': subscription['streamer_id'],
            'timestamp': datetime.utcnow().isoformat(),
            'data': data
        }

        # 发送到事件处理器
        await self._emit_event(event_data, subscription)

    async def _handle_live_end(self, data: Dict, subscription: Dict):
        """处理下播事件"""
        event_data = {
            'type': 'live_end',
            'platform': 'bilibili',
            'streamer_id': subscription['streamer_id'],
            'timestamp': datetime.utcnow().isoformat(),
            'data': data
        }

        await self._emit_event(event_data, subscription)

    async def _handle_room_change(self, data: Dict, subscription: Dict):
        """处理房间信息变更"""
        event_data = {
            'type': 'room_change',
            'platform': 'bilibili',
            'streamer_id': subscription['streamer_id'],
            'timestamp': datetime.utcnow().isoformat(),
            'data': data
        }

        await self._emit_event(event_data, subscription)

    async def _emit_event(self, event_data: Dict, subscription: Dict):
        """发送事件到通知系统"""
        # 这里集成到现有的通知系统
        from app.core.notification import NotificationService

        notification_service = NotificationService()
        await notification_service.handle_realtime_event(event_data, subscription)

#### 3. 第三方库集成方案
```python
class OrdinaryRoadIntegration:
    """集成OrdinaryRoad直播聊天客户端"""

    def __init__(self):
        self.clients = {}

    async def setup_live_chat_client(self, subscription):
        """设置直播聊天客户端"""
        platform = subscription['platform']
        room_id = subscription['streamer_id']

        # 配置客户端
        config = {
            'platform': platform,
            'room_id': room_id,
            'auto_reconnect': True,
            'heartbeat_interval': 30,
            'callbacks': {
                'on_live_start': lambda data: self._on_live_start(data, subscription),
                'on_live_end': lambda data: self._on_live_end(data, subscription),
                'on_title_change': lambda data: self._on_title_change(data, subscription),
            }
        }

        try:
            # 使用OrdinaryRoad的客户端（需要安装相应的Python包）
            from ordinaryroad_live_chat_client import LiveChatClient

            client = LiveChatClient(config)
            self.clients[f"{platform}_{room_id}"] = client

            await client.connect()
            logger.info(f"成功连接到 {platform} 房间 {room_id}")

        except ImportError:
            logger.warning("OrdinaryRoad客户端未安装，回退到自实现方案")
            raise
        except Exception as e:
            logger.error(f"OrdinaryRoad客户端连接失败: {e}")
            raise

    async def _on_live_start(self, data, subscription):
        """处理开播事件"""
        await self._emit_event('live_start', data, subscription)

    async def _on_live_end(self, data, subscription):
        """处理下播事件"""
        await self._emit_event('live_end', data, subscription)

    async def _on_title_change(self, data, subscription):
        """处理标题变更事件"""
        await self._emit_event('title_change', data, subscription)

    async def _emit_event(self, event_type, data, subscription):
        """发送事件"""
        event_data = {
            'type': event_type,
            'platform': subscription['platform'],
            'streamer_id': subscription['streamer_id'],
            'timestamp': datetime.utcnow().isoformat(),
            'data': data
        }

        # 发送到通知系统
        from app.core.notification import NotificationService
        notification_service = NotificationService()
        await notification_service.handle_realtime_event(event_data, subscription)

#### 4. 渐进式实现策略
```python
class ProgressiveMonitorImplementation:
    """渐进式监控实现策略"""

    def __init__(self):
        self.implementation_level = self._detect_implementation_level()

    def _detect_implementation_level(self) -> str:
        """检测当前可用的实现级别"""
        try:
            # 检查是否有OrdinaryRoad库
            import ordinaryroad_live_chat_client
            return "ordinaryroad"
        except ImportError:
            pass

        try:
            # 检查是否有websockets库
            import websockets
            return "websocket"
        except ImportError:
            pass

        # 回退到轮询
        return "polling"

    async def create_monitor_service(self):
        """根据可用级别创建监控服务"""
        if self.implementation_level == "ordinaryroad":
            logger.info("使用OrdinaryRoad实时监控")
            return OrdinaryRoadMonitorService()

        elif self.implementation_level == "websocket":
            logger.info("使用自实现WebSocket监控")
            return WebSocketMonitorService()

        else:
            logger.info("使用轮询监控")
            return PollingMonitorService()

class WebSocketMonitorService:
    """WebSocket监控服务"""

    def __init__(self):
        self.handlers = {
            'bilibili': BilibiliWebSocketHandler(),
            # 其他平台的WebSocket处理器
        }
        self.fallback_monitor = PollingMonitorService()

    async def start_monitoring(self, subscriptions):
        """启动WebSocket监控"""
        tasks = []

        for subscription in subscriptions:
            platform = subscription['platform']

            if platform in self.handlers:
                # 使用WebSocket
                task = self.handlers[platform].monitor_streamer(subscription)
            else:
                # 回退到轮询
                task = self.fallback_monitor.monitor_single_streamer(subscription)

            tasks.append(task)

        await asyncio.gather(*tasks, return_exceptions=True)
```

### 3. 平台适配器基类 (app/platforms/base.py)
```python
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
import aiohttp
import asyncio
from datetime import datetime

class BasePlatform(ABC):
    """平台适配器基类"""

    def __init__(self, platform_name: str, base_url: str):
        self.platform_name = platform_name
        self.base_url = base_url
        self.session: Optional[aiohttp.ClientSession] = None

    async def __aenter__(self):
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=10),
            headers={'User-Agent': 'LiveMonitor/1.0'}
        )
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()

    @abstractmethod
    async def get_streamer_status(self, streamer_id: str) -> Dict[str, Any]:
        """
        获取主播状态

        Returns:
            Dict包含以下字段:
            - streamer_id: 主播ID
            - streamer_name: 主播名称
            - is_live: 是否正在直播
            - title: 直播标题
            - viewer_count: 观看人数
            - live_url: 直播链接
            - cover_image: 封面图片
            - start_time: 开播时间
        """
        pass

    @abstractmethod
    async def search_streamer(self, keyword: str) -> List[Dict[str, Any]]:
        """搜索主播"""
        pass

    async def _make_request(self, url: str, params: Dict = None) -> Dict[str, Any]:
        """发起HTTP请求"""
        if not self.session:
            raise RuntimeError("Session not initialized")

        try:
            async with self.session.get(url, params=params) as response:
                response.raise_for_status()
                return await response.json()
        except aiohttp.ClientError as e:
            raise Exception(f"请求失败: {e}")
        except Exception as e:
            raise Exception(f"解析响应失败: {e}")

class BilibiliPlatform(BasePlatform):
    """B站平台适配器"""

    def __init__(self):
        super().__init__("bilibili", "https://api.live.bilibili.com")

    async def get_streamer_status(self, streamer_id: str) -> Dict[str, Any]:
        """获取B站主播状态"""
        url = f"{self.base_url}/room/v1/Room/get_info"
        params = {"room_id": streamer_id}

        try:
            data = await self._make_request(url, params)
            room_info = data.get("data", {})

            # 获取主播信息
            user_url = f"{self.base_url}/live_user/v1/Master/info"
            user_params = {"uid": room_info.get("uid")}
            user_data = await self._make_request(user_url, user_params)
            user_info = user_data.get("data", {}).get("info", {})

            return {
                "platform": "bilibili",
                "streamer_id": streamer_id,
                "streamer_name": user_info.get("uname", "未知主播"),
                "is_live": room_info.get("live_status") == 1,
                "title": room_info.get("title", ""),
                "viewer_count": room_info.get("online", 0),
                "live_url": f"https://live.bilibili.com/{streamer_id}",
                "cover_image": room_info.get("user_cover", ""),
                "start_time": room_info.get("live_time", ""),
                "last_updated": datetime.utcnow().isoformat()
            }
        except Exception as e:
            raise Exception(f"获取B站主播状态失败: {e}")

    async def search_streamer(self, keyword: str) -> List[Dict[str, Any]]:
        """搜索B站主播"""
        url = f"{self.base_url}/room/v1/Area/getList"
        # 实现搜索逻辑
        pass

### 4. 通知服务模块 (app/core/notification.py)
```python
from typing import List, Dict, Any
from datetime import datetime, time
import asyncio
import logging

logger = logging.getLogger(__name__)

class NotificationService:
    """通知服务"""

    def __init__(self, notification_channels: Dict):
        self.channels = notification_channels

    async def send_streamer_notification(self,
                                       user_id: str,
                                       streamer: Dict,
                                       old_status: Dict,
                                       new_status: Dict,
                                       preferences: Dict):
        """发送主播状态变化通知"""

        # 检查免打扰时段
        if self._is_quiet_time(preferences.get('quiet_hours')):
            logger.info(f"用户 {user_id} 处于免打扰时段，跳过通知")
            return

        # 确定通知类型
        notification_type = self._get_notification_type(old_status, new_status)

        # 检查用户是否启用了该类型的通知
        if not self._should_send_notification(notification_type, preferences):
            return

        # 生成通知内容
        message = self._generate_message(notification_type, streamer, new_status)

        # 获取用户启用的通知渠道
        enabled_channels = preferences.get('enabled_channels', ['wxpusher'])

        # 并发发送到各个渠道
        tasks = []
        for channel_name in enabled_channels:
            if channel_name in self.channels:
                channel = self.channels[channel_name]
                task = self._send_to_channel(channel, user_id, message)
                tasks.append(task)

        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 记录发送结果
        success_count = sum(1 for r in results if not isinstance(r, Exception))
        logger.info(f"通知发送完成: {success_count}/{len(tasks)} 成功")

    def _is_quiet_time(self, quiet_hours: Dict) -> bool:
        """检查是否在免打扰时段"""
        if not quiet_hours or not quiet_hours.get('enabled'):
            return False

        now = datetime.now().time()
        start_time = time.fromisoformat(quiet_hours.get('start_time', '22:00'))
        end_time = time.fromisoformat(quiet_hours.get('end_time', '08:00'))

        if start_time <= end_time:
            return start_time <= now <= end_time
        else:  # 跨天的情况
            return now >= start_time or now <= end_time

    def _get_notification_type(self, old_status: Dict, new_status: Dict) -> str:
        """确定通知类型"""
        if not old_status:
            return 'status_update'

        old_live = old_status.get('is_live', False)
        new_live = new_status.get('is_live', False)

        if not old_live and new_live:
            return 'live_start'
        elif old_live and not new_live:
            return 'live_end'
        elif old_status.get('title') != new_status.get('title'):
            return 'title_change'
        else:
            return 'status_update'

    def _should_send_notification(self, notification_type: str, preferences: Dict) -> bool:
        """检查是否应该发送通知"""
        type_mapping = {
            'live_start': 'notify_live_start',
            'live_end': 'notify_live_end',
            'title_change': 'notify_title_change'
        }

        pref_key = type_mapping.get(notification_type)
        if pref_key:
            return preferences.get(pref_key, True)

        return True

    def _generate_message(self, notification_type: str, streamer: Dict, status: Dict) -> str:
        """生成通知消息"""
        platform_names = {
            'bilibili': 'B站',
            'douyu': '斗鱼',
            'huya': '虎牙'
        }

        platform_name = platform_names.get(streamer['platform'], streamer['platform'])
        streamer_name = streamer['streamer_name']

        if notification_type == 'live_start':
            return f"🔴 【{platform_name}】{streamer_name} 开播了！\n\n📺 {status['title']}\n🔗 {status['live_url']}\n\n快去围观吧！"
        elif notification_type == 'live_end':
            return f"⚫ 【{platform_name}】{streamer_name} 下播了\n\n感谢观看！"
        elif notification_type == 'title_change':
            return f"📝 【{platform_name}】{streamer_name} 更新了直播标题\n\n新标题：{status['title']}\n🔗 {status['live_url']}"
        else:
            return f"📊 【{platform_name}】{streamer_name} 状态更新"

    async def _send_to_channel(self, channel, user_id: str, message: str):
        """发送到指定渠道"""
        try:
            await channel.send_message(user_id, message)
            logger.info(f"通知发送成功: {channel.__class__.__name__}")
        except Exception as e:
            logger.error(f"通知发送失败 {channel.__class__.__name__}: {e}")
            raise

### 5. 数据模型定义 (app/models/user.py)
```python
from sqlalchemy import Column, String, DateTime, Boolean, Text, Integer
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime
import uuid

Base = declarative_base()

class User(Base):
    """用户模型"""
    __tablename__ = 'users'

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    username = Column(String(50), unique=True, nullable=False, index=True)
    email = Column(String(100), unique=True, nullable=False, index=True)
    password_hash = Column(String(255), nullable=False)
    is_active = Column(Boolean, default=True)
    is_verified = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_login = Column(DateTime)

    # 用户偏好设置（JSON格式）
    notification_preferences = Column(Text)  # JSON字符串

    # 关系
    subscriptions = relationship("Subscription", back_populates="user", cascade="all, delete-orphan")

    def to_dict(self):
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'is_active': self.is_active,
            'is_verified': self.is_verified,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'last_login': self.last_login.isoformat() if self.last_login else None
        }

class Streamer(Base):
    """主播模型"""
    __tablename__ = 'streamers'

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    platform = Column(String(20), nullable=False, index=True)
    streamer_id = Column(String(100), nullable=False)  # 平台上的主播ID
    streamer_name = Column(String(100), nullable=False)
    avatar_url = Column(String(500))
    profile_url = Column(String(500))
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 最新状态缓存
    last_status = Column(Text)  # JSON字符串
    last_checked = Column(DateTime)

    # 关系
    subscriptions = relationship("Subscription", back_populates="streamer", cascade="all, delete-orphan")

    def to_dict(self):
        return {
            'id': self.id,
            'platform': self.platform,
            'streamer_id': self.streamer_id,
            'streamer_name': self.streamer_name,
            'avatar_url': self.avatar_url,
            'profile_url': self.profile_url,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

class Subscription(Base):
    """订阅模型"""
    __tablename__ = 'subscriptions'

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String(36), nullable=False, index=True)
    streamer_id = Column(String(36), nullable=False, index=True)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)

    # 个性化设置
    custom_settings = Column(Text)  # JSON字符串，存储个性化通知设置

    # 关系
    user = relationship("User", back_populates="subscriptions")
    streamer = relationship("Streamer", back_populates="subscriptions")

    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'streamer_id': self.streamer_id,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
```

## 🌐 API设计规范

### RESTful API设计原则
- **统一的响应格式**：所有API返回统一的JSON格式
- **HTTP状态码**：正确使用HTTP状态码表示请求结果
- **版本控制**：通过URL路径进行API版本控制 (`/api/v1/`)
- **认证授权**：使用JWT Bearer Token进行身份验证
- **错误处理**：提供详细的错误信息和错误码

### API响应格式
```python
# 成功响应
{
    "success": true,
    "data": {...},
    "message": "操作成功",
    "timestamp": "2024-01-01T12:00:00Z"
}

# 错误响应
{
    "success": false,
    "error": {
        "code": "VALIDATION_ERROR",
        "message": "请求参数验证失败",
        "details": {...}
    },
    "timestamp": "2024-01-01T12:00:00Z"
}
```

### 核心API端点设计

#### 1. 用户认证API (app/api/auth.py)
```python
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from pydantic import BaseModel, EmailStr
from typing import Optional

router = APIRouter(prefix="/api/v1/auth", tags=["认证"])
security = HTTPBearer()

class RegisterRequest(BaseModel):
    username: str
    email: EmailStr
    password: str

class LoginRequest(BaseModel):
    username: str
    password: str

class TokenResponse(BaseModel):
    access_token: str
    token_type: str = "bearer"
    expires_in: int

@router.post("/register", response_model=TokenResponse)
async def register(request: RegisterRequest):
    """用户注册"""
    # 实现注册逻辑
    pass

@router.post("/login", response_model=TokenResponse)
async def login(request: LoginRequest):
    """用户登录"""
    # 实现登录逻辑
    pass

@router.post("/logout")
async def logout(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """用户登出"""
    # 实现登出逻辑（可选：将token加入黑名单）
    pass

@router.get("/me")
async def get_current_user(current_user = Depends(get_current_user)):
    """获取当前用户信息"""
    return current_user.to_dict()
```

#### 2. 监控管理API (app/api/monitors.py)
```python
from fastapi import APIRouter, Depends, Query
from pydantic import BaseModel
from typing import List, Optional

router = APIRouter(prefix="/api/v1/monitors", tags=["监控管理"])

class AddStreamerRequest(BaseModel):
    platform: str
    streamer_id: str
    notification_preferences: Optional[dict] = None

class StreamerResponse(BaseModel):
    id: str
    platform: str
    streamer_id: str
    streamer_name: str
    is_live: bool
    title: Optional[str]
    viewer_count: int
    live_url: str
    last_updated: str

@router.post("/streamers")
async def add_streamer(
    request: AddStreamerRequest,
    current_user = Depends(get_current_user)
):
    """添加主播监控"""
    # 1. 验证平台和主播ID
    # 2. 检查是否已经订阅
    # 3. 获取主播信息
    # 4. 创建订阅记录
    pass

@router.get("/streamers", response_model=List[StreamerResponse])
async def get_user_streamers(
    current_user = Depends(get_current_user),
    platform: Optional[str] = Query(None, description="平台筛选"),
    is_live: Optional[bool] = Query(None, description="直播状态筛选")
):
    """获取用户订阅的主播列表"""
    # 1. 查询用户的订阅
    # 2. 获取主播的最新状态
    # 3. 应用筛选条件
    # 4. 返回结果
    pass

@router.delete("/streamers/{streamer_id}")
async def remove_streamer(
    streamer_id: str,
    current_user = Depends(get_current_user)
):
    """取消主播订阅"""
    # 1. 验证订阅关系
    # 2. 删除订阅记录
    pass

@router.put("/streamers/{streamer_id}/settings")
async def update_streamer_settings(
    streamer_id: str,
    settings: dict,
    current_user = Depends(get_current_user)
):
    """更新主播通知设置"""
    # 1. 验证订阅关系
    # 2. 更新通知设置
    pass
```

#### 3. 通知管理API (app/api/notifications.py)
```python
from fastapi import APIRouter, Depends
from pydantic import BaseModel
from typing import List, Optional

router = APIRouter(prefix="/api/v1/notifications", tags=["通知管理"])

class NotificationPreferences(BaseModel):
    notify_live_start: bool = True
    notify_live_end: bool = True
    notify_title_change: bool = True
    enabled_channels: List[str] = ["wxpusher"]
    quiet_hours: Optional[dict] = None

class NotificationHistory(BaseModel):
    id: str
    type: str
    message: str
    channel: str
    status: str
    created_at: str

@router.get("/preferences")
async def get_notification_preferences(
    current_user = Depends(get_current_user)
) -> NotificationPreferences:
    """获取通知偏好设置"""
    pass

@router.put("/preferences")
async def update_notification_preferences(
    preferences: NotificationPreferences,
    current_user = Depends(get_current_user)
):
    """更新通知偏好设置"""
    pass

@router.get("/history", response_model=List[NotificationHistory])
async def get_notification_history(
    current_user = Depends(get_current_user),
    limit: int = Query(20, le=100),
    offset: int = Query(0, ge=0)
):
    """获取通知历史记录"""
    pass

@router.post("/test")
async def send_test_notification(
    channel: str,
    current_user = Depends(get_current_user)
):
    """发送测试通知"""
    pass
```

## 🎨 前端开发指南

### Vue 3 + TypeScript 实现示例

#### 1. 用户认证组件 (frontend/src/components/Auth/LoginForm.vue)
```vue
<template>
  <div class="login-form">
    <el-form
      ref="loginFormRef"
      :model="loginForm"
      :rules="loginRules"
      label-width="80px"
      @submit.prevent="handleLogin"
    >
      <el-form-item label="用户名" prop="username">
        <el-input
          v-model="loginForm.username"
          placeholder="请输入用户名"
          :prefix-icon="User"
        />
      </el-form-item>

      <el-form-item label="密码" prop="password">
        <el-input
          v-model="loginForm.password"
          type="password"
          placeholder="请输入密码"
          :prefix-icon="Lock"
          show-password
        />
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          :loading="loading"
          @click="handleLogin"
          style="width: 100%"
        >
          登录
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { User, Lock } from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'
import { useRouter } from 'vue-router'

interface LoginForm {
  username: string
  password: string
}

const authStore = useAuthStore()
const router = useRouter()

const loginFormRef = ref()
const loading = ref(false)

const loginForm = reactive<LoginForm>({
  username: '',
  password: ''
})

const loginRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于 6 个字符', trigger: 'blur' }
  ]
}

const handleLogin = async () => {
  if (!loginFormRef.value) return

  await loginFormRef.value.validate(async (valid: boolean) => {
    if (!valid) return

    loading.value = true
    try {
      await authStore.login(loginForm)
      ElMessage.success('登录成功')
      router.push('/dashboard')
    } catch (error: any) {
      ElMessage.error(error.message || '登录失败')
    } finally {
      loading.value = false
    }
  })
}
</script>
```

#### 2. 主播监控组件 (frontend/src/components/Monitor/StreamerList.vue)
```vue
<template>
  <div class="streamer-list">
    <div class="header">
      <h2>我的订阅</h2>
      <el-button type="primary" @click="showAddDialog = true">
        <el-icon><Plus /></el-icon>
        添加主播
      </el-button>
    </div>

    <div class="filters">
      <el-select v-model="filters.platform" placeholder="选择平台" clearable>
        <el-option label="全部平台" value="" />
        <el-option label="B站" value="bilibili" />
        <el-option label="斗鱼" value="douyu" />
        <el-option label="虎牙" value="huya" />
      </el-select>

      <el-select v-model="filters.status" placeholder="直播状态" clearable>
        <el-option label="全部状态" value="" />
        <el-option label="正在直播" value="live" />
        <el-option label="未直播" value="offline" />
      </el-select>
    </div>

    <div class="streamer-grid">
      <StreamerCard
        v-for="streamer in filteredStreamers"
        :key="streamer.id"
        :streamer="streamer"
        @remove="handleRemoveStreamer"
        @settings="handleStreamerSettings"
      />
    </div>

    <!-- 添加主播对话框 -->
    <AddStreamerDialog
      v-model="showAddDialog"
      @success="handleAddSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import StreamerCard from './StreamerCard.vue'
import AddStreamerDialog from './AddStreamerDialog.vue'
import { useStreamersStore } from '@/stores/streamers'

interface Streamer {
  id: string
  platform: string
  streamer_id: string
  streamer_name: string
  is_live: boolean
  title: string
  viewer_count: number
  live_url: string
  last_updated: string
}

const streamersStore = useStreamersStore()

const showAddDialog = ref(false)
const filters = ref({
  platform: '',
  status: ''
})

const filteredStreamers = computed(() => {
  let result = streamersStore.streamers

  if (filters.value.platform) {
    result = result.filter(s => s.platform === filters.value.platform)
  }

  if (filters.value.status) {
    if (filters.value.status === 'live') {
      result = result.filter(s => s.is_live)
    } else if (filters.value.status === 'offline') {
      result = result.filter(s => !s.is_live)
    }
  }

  return result
})

onMounted(() => {
  streamersStore.fetchStreamers()
})

const handleRemoveStreamer = async (streamerId: string) => {
  try {
    await streamersStore.removeStreamer(streamerId)
    ElMessage.success('取消订阅成功')
  } catch (error: any) {
    ElMessage.error(error.message || '取消订阅失败')
  }
}

const handleStreamerSettings = (streamer: Streamer) => {
  // 打开设置对话框
}

const handleAddSuccess = () => {
  showAddDialog.value = false
  streamersStore.fetchStreamers()
  ElMessage.success('添加订阅成功')
}
</script>

<style scoped>
.streamer-list {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.filters {
  display: flex;
  gap: 16px;
  margin-bottom: 20px;
}

.streamer-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}
</style>
```

## 🔧 配置管理

### 环境配置文件 (config/development.yaml)
```yaml
# 开发环境配置
app:
  name: "直播监控系统"
  version: "1.0.0"
  debug: true
  host: "0.0.0.0"
  port: 8000

database:
  url: "postgresql://user:password@localhost:5432/live_monitor"
  echo: true  # 开发环境显示SQL
  pool_size: 5
  max_overflow: 10

redis:
  url: "redis://localhost:6379/0"
  max_connections: 10

security:
  secret_key: "your-secret-key-here"
  algorithm: "HS256"
  access_token_expire_minutes: 30

monitoring:
  check_interval: 30  # 检查间隔（秒）
  max_concurrent_checks: 10
  retry_attempts: 3
  retry_delay: 5

notifications:
  wxpusher:
    app_token: "AT_xxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
    base_url: "https://wxpusher.zjiecode.com/api"

  email:
    smtp_server: "smtp.gmail.com"
    smtp_port: 587
    username: "<EMAIL>"
    password: "your-app-password"

platforms:
  bilibili:
    base_url: "https://api.live.bilibili.com"
    rate_limit: 60  # 每分钟请求数

  douyu:
    base_url: "https://open.douyucdn.cn/api"
    rate_limit: 60

  huya:
    base_url: "https://mp.huya.com"
    rate_limit: 60

logging:
  level: "DEBUG"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "logs/app.log"
  max_bytes: 10485760  # 10MB
  backup_count: 5
```

## 🚀 部署指南

### Docker部署配置

#### 1. 后端Dockerfile (backend/Dockerfile)
```dockerfile
FROM python:3.9-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建非root用户
RUN useradd --create-home --shell /bin/bash app \
    && chown -R app:app /app
USER app

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

#### 2. 前端Dockerfile (frontend/Dockerfile)
```dockerfile
# 构建阶段
FROM node:16-alpine as build

WORKDIR /app

# 复制package文件
COPY package*.json ./

# 安装依赖
RUN npm ci --only=production

# 复制源代码
COPY . .

# 构建应用
RUN npm run build

# 生产阶段
FROM nginx:alpine

# 复制构建结果
COPY --from=build /app/dist /usr/share/nginx/html

# 复制nginx配置
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
```

#### 3. Docker Compose配置 (docker-compose.yml)
```yaml
version: '3.8'

services:
  # 数据库
  postgres:
    image: postgres:13
    environment:
      POSTGRES_DB: live_monitor
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/database/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis缓存
  redis:
    image: redis:6-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # 后端服务
  backend:
    build: ./backend
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=********************************************/live_monitor
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=your-secret-key-here
    volumes:
      - ./config:/app/config
      - ./logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped

  # 前端服务
  frontend:
    build: ./frontend
    ports:
      - "80:80"
    depends_on:
      - backend
    restart: unless-stopped

  # 监控服务（可选）
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'

volumes:
  postgres_data:
  redis_data:
  prometheus_data:
```

## 🧪 测试策略

### 1. 单元测试示例 (backend/tests/test_auth.py)
```python
import pytest
from fastapi.testclient import TestClient
from app.main import app
from app.core.auth import AuthService

client = TestClient(app)

class TestAuthService:
    def setup_method(self):
        self.auth_service = AuthService("test-secret-key")

    def test_hash_password(self):
        """测试密码哈希"""
        password = "test123456"
        hashed = self.auth_service.hash_password(password)

        assert hashed != password
        assert self.auth_service.verify_password(password, hashed)

    def test_create_access_token(self):
        """测试JWT令牌创建"""
        data = {"sub": "testuser"}
        token = self.auth_service.create_access_token(data)

        assert isinstance(token, str)
        assert len(token) > 0

    def test_verify_token(self):
        """测试JWT令牌验证"""
        data = {"sub": "testuser"}
        token = self.auth_service.create_access_token(data)
        payload = self.auth_service.verify_token(token)

        assert payload["sub"] == "testuser"

class TestAuthAPI:
    def test_register_success(self):
        """测试用户注册成功"""
        response = client.post("/api/v1/auth/register", json={
            "username": "testuser",
            "email": "<EMAIL>",
            "password": "test123456"
        })

        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
        assert data["token_type"] == "bearer"

    def test_register_duplicate_username(self):
        """测试重复用户名注册"""
        # 先注册一个用户
        client.post("/api/v1/auth/register", json={
            "username": "testuser2",
            "email": "<EMAIL>",
            "password": "test123456"
        })

        # 尝试用相同用户名注册
        response = client.post("/api/v1/auth/register", json={
            "username": "testuser2",
            "email": "<EMAIL>",
            "password": "test123456"
        })

        assert response.status_code == 400

    def test_login_success(self):
        """测试登录成功"""
        # 先注册用户
        client.post("/api/v1/auth/register", json={
            "username": "logintest",
            "email": "<EMAIL>",
            "password": "test123456"
        })

        # 登录
        response = client.post("/api/v1/auth/login", json={
            "username": "logintest",
            "password": "test123456"
        })

        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data

    def test_login_invalid_credentials(self):
        """测试无效凭据登录"""
        response = client.post("/api/v1/auth/login", json={
            "username": "nonexistent",
            "password": "wrongpassword"
        })

        assert response.status_code == 401
```

### 2. 集成测试示例 (backend/tests/test_monitor_integration.py)
```python
import pytest
import asyncio
from unittest.mock import AsyncMock, patch
from app.core.monitor import MonitorService
from app.platforms.bilibili import BilibiliPlatform

class TestMonitorIntegration:
    @pytest.fixture
    async def monitor_service(self):
        """创建监控服务实例"""
        platforms = {
            'bilibili': BilibiliPlatform()
        }
        notification_service = AsyncMock()
        cache_service = AsyncMock()

        return MonitorService(platforms, notification_service, cache_service)

    @pytest.mark.asyncio
    async def test_check_streamer_status_change(self, monitor_service):
        """测试主播状态变化检测"""
        # 模拟缓存中的旧状态
        old_status = {
            'is_live': False,
            'title': '旧标题'
        }

        # 模拟新状态
        new_status = {
            'is_live': True,
            'title': '新标题'
        }

        # 模拟平台API返回
        with patch.object(monitor_service.platforms['bilibili'], 'get_streamer_status') as mock_api:
            mock_api.return_value = new_status

            # 模拟缓存返回旧状态
            monitor_service.cache_service.get.return_value = old_status

            # 执行检查
            await monitor_service._check_platform_streamers('bilibili', [{
                'id': 'test-streamer-1',
                'streamer_id': '123456',
                'platform': 'bilibili'
            }])

            # 验证通知服务被调用
            monitor_service.notification_service.send_streamer_notification.assert_called_once()

            # 验证缓存被更新
            monitor_service.cache_service.set.assert_called_once()
```

## 📋 开发检查清单

### ✅ 后端开发
- [ ] 用户认证系统实现
- [ ] 数据库模型设计和迁移
- [ ] 平台适配器实现（B站、斗鱼、虎牙）
- [ ] 监控服务核心逻辑
- [ ] 通知系统多渠道支持
- [ ] API接口完整实现
- [ ] 单元测试覆盖率 > 80%
- [ ] 集成测试完整
- [ ] 错误处理和日志记录
- [ ] 性能优化和缓存

### ✅ 前端开发
- [ ] 用户界面设计和实现
- [ ] 用户认证流程
- [ ] 主播订阅管理
- [ ] 通知设置界面
- [ ] 数据可视化展示
- [ ] 响应式设计
- [ ] 错误处理和用户反馈
- [ ] 前端测试覆盖
- [ ] 性能优化
- [ ] 浏览器兼容性

### ✅ 部署运维
- [ ] Docker容器化配置
- [ ] 数据库部署和配置
- [ ] 环境变量管理
- [ ] 日志收集和分析
- [ ] 监控和告警设置
- [ ] 备份和恢复策略
- [ ] 安全配置
- [ ] 性能监控
- [ ] 自动化部署
- [ ] 文档完善

## 🎯 开发优先级

### 第一阶段：核心功能 (2-3周)
1. **用户认证系统**：注册、登录、JWT认证
2. **数据库设计**：用户、主播、订阅模型
3. **基础监控**：轮询监控实现（确保基础功能）
4. **基础通知**：WxPusher通知实现
5. **基础API**：用户管理、订阅管理API

### 第二阶段：实时监控升级 (2-3周)
1. **WebSocket集成**：B站WebSocket实时监控
2. **第三方库集成**：OrdinaryRoad客户端集成（可选）
3. **混合监控策略**：WebSocket + 轮询备选方案
4. **多平台支持**：斗鱼、虎牙平台适配器
5. **前端界面**：用户界面和主要功能页面

### 第三阶段：功能完善 (2-3周)
1. **通知增强**：多渠道通知、通知偏好设置
2. **实时推送优化**：连接管理、断线重连、性能优化
3. **智能监控**：自适应频率、状态变化优化检测
4. **测试覆盖**：单元测试和集成测试
5. **错误处理**：完善的异常处理和日志记录

### 第四阶段：优化部署 (1-2周)
1. **性能优化**：缓存、批处理、并发优化
2. **部署配置**：Docker、数据库、环境配置
3. **监控告警**：系统监控、WebSocket连接监控
4. **文档完善**：API文档、部署文档、用户指南
5. **安全加固**：安全配置、漏洞修复

## 🔄 WebSocket实现优先级

### 推荐实施顺序：

#### 1. **第一优先级：OrdinaryRoad集成**
```bash
# 安装OrdinaryRoad相关依赖
pip install ordinaryroad-live-chat-client  # 假设的包名
```
- **优点**：成熟稳定、多平台支持、维护良好
- **缺点**：依赖第三方、可能需要Java环境

#### 2. **第二优先级：B站WebSocket自实现**
```python
# 依赖安装
pip install websockets aiohttp
```
- **优点**：完全控制、轻量级、Python原生
- **缺点**：需要逆向工程、维护成本高

#### 3. **第三优先级：轮询备选方案**
```python
# 基础HTTP请求
pip install aiohttp requests
```
- **优点**：简单可靠、易于实现和调试
- **缺点**：实时性差、API调用频繁

### WebSocket连接监控
```python
class WebSocketConnectionMonitor:
    """WebSocket连接状态监控"""

    def __init__(self):
        self.connections = {}
        self.connection_stats = {}

    async def monitor_connections(self):
        """监控所有WebSocket连接状态"""
        while True:
            for conn_id, connection in self.connections.items():
                try:
                    # 检查连接状态
                    if connection.closed:
                        await self._handle_connection_lost(conn_id)
                    else:
                        # 更新连接统计
                        self._update_connection_stats(conn_id)

                except Exception as e:
                    logger.error(f"连接监控失败 {conn_id}: {e}")

            await asyncio.sleep(10)  # 每10秒检查一次

    async def _handle_connection_lost(self, conn_id):
        """处理连接丢失"""
        logger.warning(f"WebSocket连接丢失: {conn_id}")

        # 尝试重连
        await self._reconnect(conn_id)

        # 如果重连失败，切换到轮询模式
        if conn_id not in self.connections:
            await self._fallback_to_polling(conn_id)

    def _update_connection_stats(self, conn_id):
        """更新连接统计"""
        if conn_id not in self.connection_stats:
            self.connection_stats[conn_id] = {
                'connected_at': datetime.utcnow(),
                'last_message': None,
                'message_count': 0,
                'reconnect_count': 0
            }

        stats = self.connection_stats[conn_id]
        stats['last_heartbeat'] = datetime.utcnow()
```

---

> 🎯 **开发目标**：构建一个功能完整、性能优秀、易于维护的现代化直播监控系统，为用户提供稳定可靠的主播状态监控和通知服务。
