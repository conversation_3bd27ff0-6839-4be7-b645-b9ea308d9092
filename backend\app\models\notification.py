"""通知模型"""
import uuid
from datetime import datetime
from typing import Dict, Any, Optional

from sqlalchemy import Column, String, DateTime, Text, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from ..database import Base


class NotificationHistory(Base):
    """通知历史模型"""
    __tablename__ = 'notification_history'

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=False, index=True)
    streamer_id = Column(UUID(as_uuid=True), ForeignKey('streamers.id'), nullable=True)
    notification_type = Column(String(50), nullable=False)  # live_start, live_end, title_change
    channel = Column(String(50), nullable=False)  # wxpusher, email, sms, qq_bot
    message = Column(Text, nullable=False)
    status = Column(String(20), default='pending')  # pending, sent, failed
    error_message = Column(Text)
    created_at = Column(DateTime, default=datetime.utcnow, index=True)
    sent_at = Column(DateTime)

    # 关系
    user = relationship("User", back_populates="notification_history")
    streamer = relationship("Streamer", back_populates="notification_history")

    def __repr__(self) -> str:
        return f"<NotificationHistory(id={self.id}, type={self.notification_type}, channel={self.channel}, status={self.status})>"

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': str(self.id),
            'user_id': str(self.user_id),
            'streamer_id': str(self.streamer_id) if self.streamer_id else None,
            'notification_type': self.notification_type,
            'channel': self.channel,
            'message': self.message,
            'status': self.status,
            'error_message': self.error_message,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'sent_at': self.sent_at.isoformat() if self.sent_at else None
        }

    def mark_as_sent(self) -> None:
        """标记为已发送"""
        self.status = 'sent'
        self.sent_at = datetime.utcnow()

    def mark_as_failed(self, error_message: str) -> None:
        """标记为发送失败"""
        self.status = 'failed'
        self.error_message = error_message

    def is_pending(self) -> bool:
        """是否待发送"""
        return self.status == 'pending'

    def is_sent(self) -> bool:
        """是否已发送"""
        return self.status == 'sent'

    def is_failed(self) -> bool:
        """是否发送失败"""
        return self.status == 'failed'

    def get_notification_type_display(self) -> str:
        """获取通知类型显示名称"""
        type_names = {
            'live_start': '开播通知',
            'live_end': '下播通知',
            'title_change': '标题变更',
            'status_update': '状态更新'
        }
        return type_names.get(self.notification_type, self.notification_type)

    def get_channel_display(self) -> str:
        """获取通知渠道显示名称"""
        channel_names = {
            'wxpusher': '微信推送',
            'email': '邮件',
            'sms': '短信',
            'qq_bot': 'QQ机器人'
        }
        return channel_names.get(self.channel, self.channel)

    def get_status_display(self) -> str:
        """获取状态显示名称"""
        status_names = {
            'pending': '待发送',
            'sent': '已发送',
            'failed': '发送失败'
        }
        return status_names.get(self.status, self.status)

    def to_detailed_dict(self) -> Dict[str, Any]:
        """转换为包含详细信息的字典"""
        result = self.to_dict()
        
        # 添加显示名称
        result.update({
            'notification_type_display': self.get_notification_type_display(),
            'channel_display': self.get_channel_display(),
            'status_display': self.get_status_display()
        })
        
        # 添加主播信息
        if self.streamer:
            result['streamer'] = {
                'id': str(self.streamer.id),
                'platform': self.streamer.platform,
                'streamer_id': self.streamer.streamer_id,
                'streamer_name': self.streamer.streamer_name,
                'platform_display': self.streamer.get_platform_display_name()
            }
        
        return result

    @classmethod
    def create_notification(
        cls,
        user_id: str,
        notification_type: str,
        channel: str,
        message: str,
        streamer_id: Optional[str] = None
    ) -> 'NotificationHistory':
        """创建通知记录"""
        return cls(
            user_id=user_id,
            streamer_id=streamer_id,
            notification_type=notification_type,
            channel=channel,
            message=message
        )
