# 直播监控系统 Makefile

.PHONY: help install test dev build clean docker-build docker-up docker-down

# 默认目标
help:
	@echo "直播监控系统 - 可用命令:"
	@echo ""
	@echo "开发相关:"
	@echo "  install     - 安装依赖"
	@echo "  test        - 运行测试"
	@echo "  dev         - 启动开发服务器"
	@echo "  lint        - 代码检查"
	@echo "  format      - 代码格式化"
	@echo ""
	@echo "构建相关:"
	@echo "  build       - 构建项目"
	@echo "  clean       - 清理构建文件"
	@echo ""
	@echo "Docker相关:"
	@echo "  docker-build - 构建Docker镜像"
	@echo "  docker-up   - 启动Docker服务"
	@echo "  docker-down - 停止Docker服务"
	@echo "  docker-logs - 查看Docker日志"
	@echo ""
	@echo "数据库相关:"
	@echo "  db-init     - 初始化数据库"
	@echo "  db-migrate  - 运行数据库迁移"
	@echo "  db-reset    - 重置数据库"

# 安装依赖
install:
	@echo "📦 安装后端依赖..."
	cd backend && pip install -r requirements.txt
	@echo "✅ 依赖安装完成"

# 运行测试
test:
	@echo "🧪 运行应用测试..."
	cd backend && python test_app.py

# 启动开发服务器
dev:
	@echo "🚀 启动开发服务器..."
	python scripts/start_backend.py

# 代码检查
lint:
	@echo "🔍 运行代码检查..."
	cd backend && python -m flake8 app/
	cd backend && python -m mypy app/

# 代码格式化
format:
	@echo "🎨 格式化代码..."
	cd backend && python -m black app/
	cd backend && python -m isort app/

# 构建项目
build:
	@echo "🔨 构建项目..."
	# 这里可以添加构建步骤

# 清理构建文件
clean:
	@echo "🧹 清理构建文件..."
	find . -type d -name "__pycache__" -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete
	find . -type f -name "*.pyo" -delete
	find . -type f -name "*.pyd" -delete
	find . -type f -name ".coverage" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +

# Docker构建
docker-build:
	@echo "🐳 构建Docker镜像..."
	docker-compose build

# Docker启动
docker-up:
	@echo "🐳 启动Docker服务..."
	docker-compose up -d

# Docker停止
docker-down:
	@echo "🐳 停止Docker服务..."
	docker-compose down

# Docker日志
docker-logs:
	@echo "📋 查看Docker日志..."
	docker-compose logs -f

# 数据库初始化
db-init:
	@echo "🗄️  初始化数据库..."
	docker-compose up -d postgres
	@echo "等待数据库启动..."
	sleep 10
	cd backend && python -c "import asyncio; from app.database import create_tables; asyncio.run(create_tables())"

# 数据库迁移
db-migrate:
	@echo "🗄️  运行数据库迁移..."
	# 这里可以添加Alembic迁移命令

# 数据库重置
db-reset:
	@echo "🗄️  重置数据库..."
	docker-compose down postgres
	docker volume rm live_monitor-v2_postgres_data || true
	docker-compose up -d postgres
	@echo "等待数据库启动..."
	sleep 10
	$(MAKE) db-init

# 快速启动（开发环境）
quick-start:
	@echo "⚡ 快速启动开发环境..."
	docker-compose up -d postgres redis
	@echo "等待服务启动..."
	sleep 5
	$(MAKE) dev

# 生产环境部署
deploy:
	@echo "🚀 部署到生产环境..."
	docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

# 查看服务状态
status:
	@echo "📊 服务状态:"
	docker-compose ps

# 查看日志
logs:
	@echo "📋 查看应用日志..."
	tail -f logs/app.log

# 备份数据库
backup:
	@echo "💾 备份数据库..."
	docker-compose exec postgres pg_dump -U postgres live_monitor > backup_$(shell date +%Y%m%d_%H%M%S).sql

# 恢复数据库
restore:
	@echo "🔄 恢复数据库..."
	@read -p "请输入备份文件名: " backup_file; \
	docker-compose exec -T postgres psql -U postgres live_monitor < $$backup_file
