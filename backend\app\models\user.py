"""用户模型"""
import json
import uuid
from datetime import datetime
from typing import Dict, Any, Optional, List

from sqlalchemy import Column, String, DateTime, Boolean, Text
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from ..database import Base


class User(Base):
    """用户模型"""
    __tablename__ = 'users'

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    username = Column(String(50), unique=True, nullable=False, index=True)
    email = Column(String(100), unique=True, nullable=False, index=True)
    password_hash = Column(String(255), nullable=False)
    is_active = Column(Boolean, default=True)
    is_verified = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_login = Column(DateTime)

    # 用户偏好设置（JSON格式）
    notification_preferences = Column(Text)

    # 关系
    subscriptions = relationship("Subscription", back_populates="user", cascade="all, delete-orphan")
    notification_history = relationship("NotificationHistory", back_populates="user", cascade="all, delete-orphan")

    def __repr__(self) -> str:
        return f"<User(id={self.id}, username={self.username}, email={self.email})>"

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': str(self.id),
            'username': self.username,
            'email': self.email,
            'is_active': self.is_active,
            'is_verified': self.is_verified,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'last_login': self.last_login.isoformat() if self.last_login else None,
            'notification_preferences': self.get_notification_preferences()
        }

    def get_notification_preferences(self) -> Dict[str, Any]:
        """获取通知偏好设置"""
        if self.notification_preferences:
            try:
                return json.loads(self.notification_preferences)
            except json.JSONDecodeError:
                pass
        
        # 返回默认设置
        return {
            'notify_live_start': True,
            'notify_live_end': True,
            'notify_title_change': True,
            'enabled_channels': ['wxpusher'],
            'quiet_hours': {
                'enabled': False,
                'start_time': '22:00',
                'end_time': '08:00'
            }
        }

    def set_notification_preferences(self, preferences: Dict[str, Any]) -> None:
        """设置通知偏好"""
        self.notification_preferences = json.dumps(preferences, ensure_ascii=False)

    def update_last_login(self) -> None:
        """更新最后登录时间"""
        self.last_login = datetime.utcnow()

    def is_notification_enabled(self, notification_type: str) -> bool:
        """检查是否启用了指定类型的通知"""
        preferences = self.get_notification_preferences()
        type_mapping = {
            'live_start': 'notify_live_start',
            'live_end': 'notify_live_end',
            'title_change': 'notify_title_change'
        }
        
        pref_key = type_mapping.get(notification_type)
        if pref_key:
            return preferences.get(pref_key, True)
        
        return True

    def get_enabled_channels(self) -> List[str]:
        """获取启用的通知渠道"""
        preferences = self.get_notification_preferences()
        return preferences.get('enabled_channels', ['wxpusher'])

    def is_in_quiet_hours(self) -> bool:
        """检查是否在免打扰时段"""
        preferences = self.get_notification_preferences()
        quiet_hours = preferences.get('quiet_hours', {})
        
        if not quiet_hours.get('enabled', False):
            return False
        
        from datetime import time
        
        now = datetime.now().time()
        start_time = time.fromisoformat(quiet_hours.get('start_time', '22:00'))
        end_time = time.fromisoformat(quiet_hours.get('end_time', '08:00'))
        
        if start_time <= end_time:
            return start_time <= now <= end_time
        else:  # 跨天的情况
            return now >= start_time or now <= end_time
