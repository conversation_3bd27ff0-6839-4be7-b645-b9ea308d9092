import { request } from './index'

// 用户接口
export interface User {
  id: string
  username: string
  email: string
  is_active: boolean
  is_verified: boolean
  created_at: string
  updated_at: string
  last_login?: string
  notification_preferences: {
    notify_live_start: boolean
    notify_live_end: boolean
    notify_title_change: boolean
    enabled_channels: string[]
    quiet_hours: {
      enabled: boolean
      start_time: string
      end_time: string
    }
  }
}

// 登录请求
export interface LoginRequest {
  username: string
  password: string
}

// 注册请求
export interface RegisterRequest {
  username: string
  email: string
  password: string
}

// 登录响应
export interface LoginResponse {
  access_token: string
  token_type: string
  expires_in: number
  user: User
}

// 修改密码请求
export interface ChangePasswordRequest {
  old_password: string
  new_password: string
}

// 认证API
export const authApi = {
  // 用户登录
  login(data: LoginRequest): Promise<LoginResponse> {
    return request.post('/v1/auth/login', data)
  },

  // 用户注册
  register(data: RegisterRequest): Promise<LoginResponse> {
    return request.post('/v1/auth/register', data)
  },

  // 获取当前用户信息
  getCurrentUser(): Promise<User> {
    return request.get('/v1/auth/me')
  },

  // 验证token
  verifyToken(): Promise<{ valid: boolean; user: User }> {
    return request.get('/v1/auth/verify-token')
  },

  // 修改密码
  changePassword(data: ChangePasswordRequest): Promise<{ message: string }> {
    return request.put('/v1/auth/password', data)
  },

  // 登出
  logout(): Promise<{ message: string }> {
    return request.post('/v1/auth/logout')
  }
}
