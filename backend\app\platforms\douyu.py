"""斗鱼平台适配器"""
from typing import Dict, Any, List, Optional
from datetime import datetime
import re

from .base import BasePlatform
from ..utils import get_logger

logger = get_logger("douyu")


class DouyuPlatform(BasePlatform):
    """斗鱼平台适配器"""

    def __init__(self, base_url: str = "https://www.douyu.com", rate_limit: int = 60):
        super().__init__("douyu", base_url, rate_limit)

    async def get_streamer_status(self, streamer_id: str) -> Dict[str, Any]:
        """获取斗鱼主播状态"""
        try:
            # 斗鱼没有公开API，需要解析网页
            room_data = await self._get_room_data(streamer_id)
            if not room_data:
                raise Exception(f"房间不存在: {streamer_id}")

            # 构造标准化状态
            status = {
                'platform': self.platform_name,
                'streamer_id': streamer_id,
                'streamer_name': room_data.get('owner_name', '未知主播'),
                'is_live': room_data.get('show_status') == 1,
                'title': room_data.get('room_name', ''),
                'viewer_count': room_data.get('online', 0),
                'live_url': f"https://www.douyu.com/{streamer_id}",
                'cover_image': room_data.get('room_src', ''),
                'avatar_url': room_data.get('avatar', ''),
                'profile_url': f"https://www.douyu.com/{streamer_id}",
                'start_time': room_data.get('start_time', ''),
                'last_updated': datetime.utcnow().isoformat()
            }

            logger.debug(f"斗鱼主播状态获取成功: {streamer_id}, 直播状态: {status['is_live']}")
            return status

        except Exception as e:
            logger.error(f"获取斗鱼主播状态失败: {streamer_id}, 错误: {e}")
            raise

    async def _get_room_data(self, room_id: str) -> Optional[Dict[str, Any]]:
        """获取房间数据"""
        url = f"/{room_id}"

        try:
            response = await self._make_request(url)
            
            # 斗鱼返回的是HTML，需要解析
            html_content = response.get("text", "")
            if not html_content:
                return None

            # 提取房间信息（简化版本，实际需要更复杂的解析）
            room_data = self._parse_room_html(html_content)
            return room_data

        except Exception as e:
            logger.error(f"斗鱼房间数据请求失败: {room_id}, 错误: {e}")
            return None

    def _parse_room_html(self, html: str) -> Dict[str, Any]:
        """解析房间HTML页面"""
        room_data = {}
        
        try:
            # 提取房间名称
            title_match = re.search(r'<title>(.*?)-斗鱼', html)
            if title_match:
                room_data['room_name'] = title_match.group(1).strip()

            # 提取主播名称
            owner_match = re.search(r'"owner_name":"([^"]*)"', html)
            if owner_match:
                room_data['owner_name'] = owner_match.group(1)

            # 提取直播状态
            status_match = re.search(r'"show_status":(\d+)', html)
            if status_match:
                room_data['show_status'] = int(status_match.group(1))

            # 提取在线人数
            online_match = re.search(r'"online":(\d+)', html)
            if online_match:
                room_data['online'] = int(online_match.group(1))

            # 提取封面图片
            cover_match = re.search(r'"room_src":"([^"]*)"', html)
            if cover_match:
                room_data['room_src'] = cover_match.group(1)

            # 提取头像
            avatar_match = re.search(r'"avatar":"([^"]*)"', html)
            if avatar_match:
                room_data['avatar'] = avatar_match.group(1)

        except Exception as e:
            logger.error(f"解析斗鱼房间HTML失败: {e}")

        return room_data

    async def search_streamer(self, keyword: str) -> List[Dict[str, Any]]:
        """搜索斗鱼主播"""
        # 斗鱼搜索需要特殊处理，这里提供基础实现
        url = "/search/"
        params = {
            "sk": keyword,
            "type": "live"
        }

        try:
            response = await self._make_request(url, params)
            html_content = response.get("text", "")
            
            # 解析搜索结果（简化版本）
            results = self._parse_search_html(html_content, keyword)
            
            logger.debug(f"斗鱼主播搜索完成: {keyword}, 找到 {len(results)} 个结果")
            return results

        except Exception as e:
            logger.error(f"斗鱼主播搜索失败: {keyword}, 错误: {e}")
            return []

    def _parse_search_html(self, html: str, keyword: str) -> List[Dict[str, Any]]:
        """解析搜索结果HTML"""
        results = []
        
        try:
            # 这里需要根据斗鱼的实际HTML结构来解析
            # 由于斗鱼的页面结构可能经常变化，这里提供基础框架
            
            # 使用正则表达式提取房间信息
            room_pattern = r'data-rid="(\d+)".*?title="([^"]*)".*?data-avatar="([^"]*)"'
            matches = re.findall(room_pattern, html, re.DOTALL)
            
            for match in matches:
                room_id, title, avatar = match
                result = {
                    'platform': self.platform_name,
                    'streamer_id': room_id,
                    'streamer_name': title,
                    'title': title,
                    'is_live': True,  # 搜索结果通常是正在直播的
                    'viewer_count': 0,  # 搜索页面可能不显示观看人数
                    'cover_image': '',
                    'avatar_url': avatar,
                    'live_url': f"https://www.douyu.com/{room_id}",
                    'profile_url': f"https://www.douyu.com/{room_id}"
                }
                results.append(result)

        except Exception as e:
            logger.error(f"解析斗鱼搜索结果失败: {e}")

        return results

    async def validate_streamer_id(self, streamer_id: str) -> bool:
        """验证斗鱼房间ID是否有效"""
        try:
            room_data = await self._get_room_data(streamer_id)
            return room_data is not None and room_data.get('owner_name')
        except Exception:
            return False

    def _get_default_headers(self) -> Dict[str, str]:
        """获取斗鱼专用请求头"""
        headers = super()._get_default_headers()
        headers.update({
            'Referer': 'https://www.douyu.com/',
            'Origin': 'https://www.douyu.com'
        })
        return headers
