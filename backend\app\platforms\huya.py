"""虎牙平台适配器"""
from typing import Dict, Any, List, Optional
from datetime import datetime
import re

from .base import BasePlatform
from ..utils import get_logger

logger = get_logger("huya")


class HuyaPlatform(BasePlatform):
    """虎牙平台适配器"""

    def __init__(self, base_url: str = "https://www.huya.com", rate_limit: int = 60):
        super().__init__("huya", base_url, rate_limit)

    async def get_streamer_status(self, streamer_id: str) -> Dict[str, Any]:
        """获取虎牙主播状态"""
        try:
            # 虎牙没有公开API，需要解析网页
            room_data = await self._get_room_data(streamer_id)
            if not room_data:
                raise Exception(f"房间不存在: {streamer_id}")

            # 构造标准化状态
            status = {
                'platform': self.platform_name,
                'streamer_id': streamer_id,
                'streamer_name': room_data.get('nick', '未知主播'),
                'is_live': room_data.get('isOn', False),
                'title': room_data.get('introduction', ''),
                'viewer_count': room_data.get('totalCount', 0),
                'live_url': f"https://www.huya.com/{streamer_id}",
                'cover_image': room_data.get('screenshot', ''),
                'avatar_url': room_data.get('avatar180', ''),
                'profile_url': f"https://www.huya.com/{streamer_id}",
                'start_time': room_data.get('startTime', ''),
                'last_updated': datetime.utcnow().isoformat()
            }

            logger.debug(f"虎牙主播状态获取成功: {streamer_id}, 直播状态: {status['is_live']}")
            return status

        except Exception as e:
            logger.error(f"获取虎牙主播状态失败: {streamer_id}, 错误: {e}")
            raise

    async def _get_room_data(self, room_id: str) -> Optional[Dict[str, Any]]:
        """获取房间数据"""
        url = f"/{room_id}"

        try:
            response = await self._make_request(url)
            
            # 虎牙返回的是HTML，需要解析
            html_content = response.get("text", "")
            if not html_content:
                return None

            # 提取房间信息
            room_data = self._parse_room_html(html_content)
            return room_data

        except Exception as e:
            logger.error(f"虎牙房间数据请求失败: {room_id}, 错误: {e}")
            return None

    def _parse_room_html(self, html: str) -> Dict[str, Any]:
        """解析房间HTML页面"""
        room_data = {}
        
        try:
            # 提取房间信息的JSON数据
            # 虎牙通常在页面中嵌入JSON数据
            json_match = re.search(r'window\.HNF_GLOBAL_INIT\s*=\s*({.*?});', html, re.DOTALL)
            if json_match:
                import json
                try:
                    global_data = json.loads(json_match.group(1))
                    room_info = global_data.get('roomInfo', {})
                    
                    room_data.update({
                        'nick': room_info.get('tProfileInfo', {}).get('sNick', ''),
                        'introduction': room_info.get('tLiveInfo', {}).get('sIntroduction', ''),
                        'isOn': room_info.get('eLiveStatus') == 2,  # 2表示正在直播
                        'totalCount': room_info.get('lTotalCount', 0),
                        'screenshot': room_info.get('tLiveInfo', {}).get('sScreenshot', ''),
                        'avatar180': room_info.get('tProfileInfo', {}).get('sAvatar180', ''),
                        'startTime': room_info.get('tLiveInfo', {}).get('sStartTime', '')
                    })
                except json.JSONDecodeError:
                    pass

            # 如果JSON解析失败，尝试其他方法
            if not room_data:
                # 提取标题
                title_match = re.search(r'<title>(.*?)-虎牙直播', html)
                if title_match:
                    room_data['introduction'] = title_match.group(1).strip()

                # 提取主播名称
                nick_match = re.search(r'"nick":"([^"]*)"', html)
                if nick_match:
                    room_data['nick'] = nick_match.group(1)

                # 提取直播状态
                status_match = re.search(r'"eLiveStatus":(\d+)', html)
                if status_match:
                    room_data['isOn'] = int(status_match.group(1)) == 2

                # 提取在线人数
                count_match = re.search(r'"lTotalCount":(\d+)', html)
                if count_match:
                    room_data['totalCount'] = int(count_match.group(1))

        except Exception as e:
            logger.error(f"解析虎牙房间HTML失败: {e}")

        return room_data

    async def search_streamer(self, keyword: str) -> List[Dict[str, Any]]:
        """搜索虎牙主播"""
        url = "/search"
        params = {
            "hsk": keyword,
            "ht": "live"
        }

        try:
            response = await self._make_request(url, params)
            html_content = response.get("text", "")
            
            # 解析搜索结果
            results = self._parse_search_html(html_content, keyword)
            
            logger.debug(f"虎牙主播搜索完成: {keyword}, 找到 {len(results)} 个结果")
            return results

        except Exception as e:
            logger.error(f"虎牙主播搜索失败: {keyword}, 错误: {e}")
            return []

    def _parse_search_html(self, html: str, keyword: str) -> List[Dict[str, Any]]:
        """解析搜索结果HTML"""
        results = []
        
        try:
            # 提取搜索结果中的房间信息
            # 虎牙搜索结果的HTML结构
            room_pattern = r'data-rid="([^"]*)".*?title="([^"]*)".*?data-avatar="([^"]*)"'
            matches = re.findall(room_pattern, html, re.DOTALL)
            
            for match in matches:
                room_id, title, avatar = match
                result = {
                    'platform': self.platform_name,
                    'streamer_id': room_id,
                    'streamer_name': title,
                    'title': title,
                    'is_live': True,  # 搜索结果通常是正在直播的
                    'viewer_count': 0,  # 搜索页面可能不显示观看人数
                    'cover_image': '',
                    'avatar_url': avatar,
                    'live_url': f"https://www.huya.com/{room_id}",
                    'profile_url': f"https://www.huya.com/{room_id}"
                }
                results.append(result)

        except Exception as e:
            logger.error(f"解析虎牙搜索结果失败: {e}")

        return results

    async def validate_streamer_id(self, streamer_id: str) -> bool:
        """验证虎牙房间ID是否有效"""
        try:
            room_data = await self._get_room_data(streamer_id)
            return room_data is not None and room_data.get('nick')
        except Exception:
            return False

    def _get_default_headers(self) -> Dict[str, str]:
        """获取虎牙专用请求头"""
        headers = super()._get_default_headers()
        headers.update({
            'Referer': 'https://www.huya.com/',
            'Origin': 'https://www.huya.com'
        })
        return headers
