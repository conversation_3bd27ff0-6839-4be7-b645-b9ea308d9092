# 开发环境配置
app:
  name: "直播监控系统"
  version: "1.0.0"
  debug: true
  host: "0.0.0.0"
  port: 8000

database:
  url: "postgresql://postgres:password@localhost:5432/live_monitor"
  echo: true  # 开发环境显示SQL
  pool_size: 5
  max_overflow: 10

redis:
  url: "redis://localhost:6379/0"
  max_connections: 10

security:
  secret_key: "your-secret-key-here"
  algorithm: "HS256"
  access_token_expire_minutes: 30

monitoring:
  check_interval: 30  # 检查间隔（秒）
  max_concurrent_checks: 10
  retry_attempts: 3
  retry_delay: 5

notifications:
  wxpusher:
    app_token: "AT_xxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
    base_url: "https://wxpusher.zjiecode.com/api"

  email:
    smtp_server: "smtp.gmail.com"
    smtp_port: 587
    username: "<EMAIL>"
    password: "your-app-password"

platforms:
  bilibili:
    base_url: "https://api.live.bilibili.com"
    rate_limit: 60  # 每分钟请求数

  douyu:
    base_url: "https://open.douyucdn.cn/api"
    rate_limit: 60

  huya:
    base_url: "https://mp.huya.com"
    rate_limit: 60

logging:
  level: "DEBUG"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "logs/app.log"
  max_bytes: 10485760  # 10MB
  backup_count: 5
