# 🎯 直播监控系统 (Live Monitor v2)

一个现代化的直播监控系统，用于实时监控多个直播平台（B站、斗鱼、虎牙等）的主播状态，并通过多种渠道向用户发送通知。

## ✨ 主要功能

- 🔐 **用户管理**：注册、登录、JWT认证
- 📺 **多平台监控**：支持B站、斗鱼、虎牙等主流直播平台
- 🔔 **多渠道通知**：微信推送、邮件、短信、QQ机器人
- ⚡ **实时监控**：WebSocket实时推送 + 轮询备选方案
- 🎨 **现代化界面**：Vue 3 + TypeScript + Element Plus
- 🐳 **容器化部署**：Docker + Docker Compose

## 🏗️ 技术架构

### 后端技术栈
- **Python 3.9+** + **FastAPI**
- **PostgreSQL** + **Redis**
- **SQLAlchemy** + **Alembic**
- **asyncio** + **aiohttp** + **websockets**

### 前端技术栈
- **Vue 3** + **TypeScript**
- **Element Plus** + **Tailwind CSS**
- **Pinia** + **Vue Router**
- **Vite** + **ESLint** + **Prettier**

## 🚀 快速开始

### 环境要求
- Python 3.9+
- Node.js 16+
- PostgreSQL 13+
- Redis 6+

### 开发环境启动

1. **克隆项目**
```bash
git clone <repository-url>
cd live_monitor-v2
```

2. **启动数据库服务**
```bash
docker-compose up -d postgres redis
```

3. **启动后端服务**
```bash
cd backend
pip install -r requirements.txt
python -m app.main
```

4. **启动前端服务**
```bash
cd frontend
npm install
npm run dev
```

### Docker部署

```bash
# 构建并启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

## 📁 项目结构

```
live_monitor-v2/
├── backend/                 # 后端服务
│   ├── app/                # 主应用
│   │   ├── api/           # API路由
│   │   ├── core/          # 核心业务逻辑
│   │   ├── models/        # 数据模型
│   │   ├── platforms/     # 平台适配器
│   │   ├── notifications/ # 通知渠道
│   │   └── utils/         # 工具模块
│   ├── tests/             # 测试代码
│   └── requirements.txt   # Python依赖
├── frontend/              # 前端应用
│   ├── src/              # 源代码
│   │   ├── components/   # 组件
│   │   ├── views/        # 页面
│   │   ├── stores/       # 状态管理
│   │   └── api/          # API调用
│   └── package.json      # 依赖配置
├── config/               # 配置文件
├── docs/                 # 文档
└── docker-compose.yml    # Docker配置
```

## 📖 文档

- [API文档](docs/api.md)
- [部署文档](docs/deployment.md)
- [开发文档](docs/development.md)

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License
