"""通知系统模块"""
from typing import Dict, List, Optional
import asyncio

from .base import BaseNotificationChannel
from .wxpusher import WxPusherChannel
from .email import EmailChannel
from ..utils import get_logger

logger = get_logger("notifications")


class NotificationManager:
    """通知管理器"""
    
    def __init__(self):
        self.channels: Dict[str, BaseNotificationChannel] = {}
        self._initialized = False
    
    async def initialize(self) -> None:
        """初始化所有通知渠道"""
        if self._initialized:
            return
        
        # 初始化WxPusher渠道
        wxpusher = WxPusherChannel()
        if await wxpusher.validate_config():
            self.channels['wxpusher'] = wxpusher
            logger.info("WxPusher通知渠道初始化成功")
        else:
            logger.warning("WxPusher通知渠道配置无效，跳过初始化")
        
        # 初始化邮件渠道
        email = EmailChannel()
        if await email.validate_config():
            self.channels['email'] = email
            logger.info("邮件通知渠道初始化成功")
        else:
            logger.warning("邮件通知渠道配置无效，跳过初始化")
        
        self._initialized = True
        logger.info(f"通知管理器初始化完成，可用渠道: {list(self.channels.keys())}")
    
    async def close(self) -> None:
        """关闭所有通知渠道"""
        for channel_name, channel in self.channels.items():
            try:
                if hasattr(channel, 'close'):
                    await channel.close()
                logger.info(f"通知渠道关闭: {channel_name}")
            except Exception as e:
                logger.error(f"通知渠道关闭失败: {channel_name}, 错误: {e}")
        
        self.channels.clear()
        self._initialized = False
        logger.info("所有通知渠道已关闭")
    
    def get_channel(self, channel_name: str) -> Optional[BaseNotificationChannel]:
        """获取通知渠道
        
        Args:
            channel_name: 渠道名称
            
        Returns:
            通知渠道实例
        """
        return self.channels.get(channel_name)
    
    def get_available_channels(self) -> List[str]:
        """获取可用的通知渠道列表"""
        return [name for name, channel in self.channels.items() if channel.is_enabled()]
    
    def is_channel_available(self, channel_name: str) -> bool:
        """检查通知渠道是否可用"""
        channel = self.get_channel(channel_name)
        return channel is not None and channel.is_enabled()
    
    async def send_notification(
        self,
        user_id: str,
        channels: List[str],
        notification_type: str,
        message: str,
        **kwargs
    ) -> Dict[str, bool]:
        """发送通知到多个渠道
        
        Args:
            user_id: 用户ID
            channels: 通知渠道列表
            notification_type: 通知类型
            message: 消息内容
            **kwargs: 额外参数
            
        Returns:
            各渠道发送结果
        """
        if not self._initialized:
            await self.initialize()
        
        results = {}
        tasks = []
        
        for channel_name in channels:
            channel = self.get_channel(channel_name)
            if channel and channel.is_enabled():
                task = self._send_to_channel(
                    channel, user_id, notification_type, message, **kwargs
                )
                tasks.append((channel_name, task))
            else:
                results[channel_name] = False
                logger.warning(f"通知渠道不可用: {channel_name}")
        
        # 并发发送到所有渠道
        if tasks:
            task_results = await asyncio.gather(
                *[task for _, task in tasks], 
                return_exceptions=True
            )
            
            for i, (channel_name, _) in enumerate(tasks):
                result = task_results[i]
                if isinstance(result, Exception):
                    logger.error(f"通知发送异常: {channel_name}, 错误: {result}")
                    results[channel_name] = False
                else:
                    results[channel_name] = result
        
        # 统计发送结果
        success_count = sum(1 for success in results.values() if success)
        total_count = len(results)
        
        logger.info(f"通知发送完成: {success_count}/{total_count} 成功, 用户: {user_id}")
        
        return results
    
    async def _send_to_channel(
        self,
        channel: BaseNotificationChannel,
        user_id: str,
        notification_type: str,
        message: str,
        **kwargs
    ) -> bool:
        """发送到指定渠道"""
        try:
            return await channel.send_notification(
                user_id, notification_type, message, kwargs
            )
        except Exception as e:
            logger.error(f"渠道发送失败: {channel.get_channel_name()}, 错误: {e}")
            return False
    
    async def send_streamer_notification(
        self,
        user_id: str,
        channels: List[str],
        notification_type: str,
        streamer_name: str,
        platform_name: str,
        **kwargs
    ) -> Dict[str, bool]:
        """发送主播通知
        
        Args:
            user_id: 用户ID
            channels: 通知渠道列表
            notification_type: 通知类型
            streamer_name: 主播名称
            platform_name: 平台名称
            **kwargs: 其他参数
            
        Returns:
            各渠道发送结果
        """
        # 生成消息内容
        message = self._format_streamer_message(
            notification_type, streamer_name, platform_name, **kwargs
        )
        
        return await self.send_notification(
            user_id, channels, notification_type, message,
            streamer_name=streamer_name,
            platform_name=platform_name,
            **kwargs
        )
    
    def _format_streamer_message(
        self,
        notification_type: str,
        streamer_name: str,
        platform_name: str,
        **kwargs
    ) -> str:
        """格式化主播通知消息"""
        if notification_type == 'live_start':
            title = kwargs.get('title', '')
            live_url = kwargs.get('live_url', '')
            return f"🔴 【{platform_name}】{streamer_name} 开播了！\n\n📺 {title}\n🔗 {live_url}\n\n快去围观吧！"
        
        elif notification_type == 'live_end':
            return f"⚫ 【{platform_name}】{streamer_name} 下播了\n\n感谢观看！"
        
        elif notification_type == 'title_change':
            title = kwargs.get('title', '')
            live_url = kwargs.get('live_url', '')
            return f"📝 【{platform_name}】{streamer_name} 更新了直播标题\n\n新标题：{title}\n🔗 {live_url}"
        
        else:
            return f"📊 【{platform_name}】{streamer_name} 状态更新"
    
    async def test_channel(self, channel_name: str) -> bool:
        """测试通知渠道"""
        channel = self.get_channel(channel_name)
        if not channel:
            logger.error(f"通知渠道不存在: {channel_name}")
            return False
        
        return await channel.test_connection()
    
    async def test_all_channels(self) -> Dict[str, bool]:
        """测试所有通知渠道"""
        results = {}
        
        for channel_name in self.channels:
            results[channel_name] = await self.test_channel(channel_name)
        
        return results


# 全局通知管理器实例
notification_manager = NotificationManager()


__all__ = [
    "BaseNotificationChannel",
    "WxPusherChannel",
    "EmailChannel", 
    "NotificationManager",
    "notification_manager"
]
