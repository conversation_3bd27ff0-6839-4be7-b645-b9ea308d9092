"""后端启动脚本"""
import os
import sys
import asyncio
import subprocess
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def check_dependencies():
    """检查依赖"""
    print("🔍 检查Python依赖...")
    
    required_packages = [
        'fastapi',
        'uvicorn',
        'sqlalchemy',
        'psycopg2-binary',
        'redis',
        'aiohttp',
        'pydantic',
        'python-jose',
        'passlib'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"  ✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"  ❌ {package}")
    
    if missing_packages:
        print(f"\n💥 缺少依赖包: {', '.join(missing_packages)}")
        print("请运行以下命令安装依赖:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    print("✅ 所有依赖检查通过")
    return True


def check_environment():
    """检查环境变量"""
    print("\n🔍 检查环境配置...")
    
    # 检查配置文件
    config_file = project_root / "config" / "development.yaml"
    if config_file.exists():
        print("  ✅ 配置文件存在")
    else:
        print("  ⚠️  配置文件不存在，将使用默认配置")
    
    # 检查日志目录
    log_dir = project_root / "logs"
    if not log_dir.exists():
        log_dir.mkdir(parents=True, exist_ok=True)
        print("  ✅ 创建日志目录")
    else:
        print("  ✅ 日志目录存在")
    
    return True


async def test_application():
    """测试应用"""
    print("\n🧪 运行应用测试...")
    
    try:
        # 运行测试脚本
        backend_dir = project_root / "backend"
        test_script = backend_dir / "test_app.py"
        
        if test_script.exists():
            os.chdir(backend_dir)
            result = subprocess.run([sys.executable, "test_app.py"], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ 应用测试通过")
                return True
            else:
                print("❌ 应用测试失败")
                print(result.stdout)
                print(result.stderr)
                return False
        else:
            print("⚠️  测试脚本不存在，跳过测试")
            return True
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False


def start_server():
    """启动服务器"""
    print("\n🚀 启动后端服务器...")
    
    backend_dir = project_root / "backend"
    os.chdir(backend_dir)
    
    # 设置环境变量
    env = os.environ.copy()
    env['PYTHONPATH'] = str(backend_dir)
    
    try:
        # 启动uvicorn服务器
        cmd = [
            sys.executable, "-m", "uvicorn",
            "app.main:app",
            "--host", "0.0.0.0",
            "--port", "8000",
            "--reload",
            "--log-level", "info"
        ]
        
        print(f"执行命令: {' '.join(cmd)}")
        print("服务器启动中...")
        print("访问地址: http://localhost:8000")
        print("API文档: http://localhost:8000/docs")
        print("按 Ctrl+C 停止服务器")
        
        subprocess.run(cmd, env=env)
        
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")


async def main():
    """主函数"""
    print("🎯 直播监控系统 - 后端启动器")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        return 1
    
    # 检查环境
    if not check_environment():
        return 1
    
    # 询问是否运行测试
    run_test = input("\n🤔 是否运行应用测试？(y/N): ").lower().strip()
    if run_test in ['y', 'yes']:
        if not await test_application():
            print("⚠️  测试失败，但您仍可以尝试启动服务器")
            continue_start = input("是否继续启动？(y/N): ").lower().strip()
            if continue_start not in ['y', 'yes']:
                return 1
    
    # 启动服务器
    start_server()
    
    return 0


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
