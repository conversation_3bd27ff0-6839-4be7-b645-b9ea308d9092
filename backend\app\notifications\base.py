"""通知渠道基类"""
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from datetime import datetime

from ..utils import get_logger

logger = get_logger("notification")


class BaseNotificationChannel(ABC):
    """通知渠道基类"""

    def __init__(self, channel_name: str):
        """
        Args:
            channel_name: 渠道名称
        """
        self.channel_name = channel_name
        self.enabled = True

    @abstractmethod
    async def send_message(self, user_id: str, message: str, **kwargs) -> bool:
        """
        发送消息

        Args:
            user_id: 用户ID
            message: 消息内容
            **kwargs: 额外参数

        Returns:
            是否发送成功
        """
        pass

    @abstractmethod
    async def validate_config(self) -> bool:
        """
        验证配置是否正确

        Returns:
            配置是否有效
        """
        pass

    async def send_notification(
        self,
        user_id: str,
        notification_type: str,
        message: str,
        extra_data: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        发送通知

        Args:
            user_id: 用户ID
            notification_type: 通知类型
            message: 消息内容
            extra_data: 额外数据

        Returns:
            是否发送成功
        """
        if not self.enabled:
            logger.warning(f"通知渠道已禁用: {self.channel_name}")
            return False

        try:
            logger.info(f"发送通知: {self.channel_name}, 用户: {user_id}, 类型: {notification_type}")
            
            # 记录发送时间
            start_time = datetime.utcnow()
            
            # 发送消息
            success = await self.send_message(user_id, message, **(extra_data or {}))
            
            # 计算耗时
            duration = (datetime.utcnow() - start_time).total_seconds()
            
            if success:
                logger.info(f"通知发送成功: {self.channel_name}, 耗时: {duration:.2f}s")
            else:
                logger.error(f"通知发送失败: {self.channel_name}")
            
            return success

        except Exception as e:
            logger.error(f"通知发送异常: {self.channel_name}, 错误: {e}")
            return False

    def enable(self) -> None:
        """启用通知渠道"""
        self.enabled = True
        logger.info(f"通知渠道已启用: {self.channel_name}")

    def disable(self) -> None:
        """禁用通知渠道"""
        self.enabled = False
        logger.info(f"通知渠道已禁用: {self.channel_name}")

    def is_enabled(self) -> bool:
        """检查是否启用"""
        return self.enabled

    def get_channel_name(self) -> str:
        """获取渠道名称"""
        return self.channel_name

    def get_display_name(self) -> str:
        """获取显示名称"""
        channel_names = {
            'wxpusher': '微信推送',
            'email': '邮件',
            'sms': '短信',
            'qq_bot': 'QQ机器人'
        }
        return channel_names.get(self.channel_name, self.channel_name)

    async def test_connection(self) -> bool:
        """测试连接"""
        try:
            return await self.validate_config()
        except Exception as e:
            logger.error(f"测试连接失败: {self.channel_name}, 错误: {e}")
            return False

    def format_message(
        self,
        notification_type: str,
        streamer_name: str,
        platform_name: str,
        **kwargs
    ) -> str:
        """
        格式化消息

        Args:
            notification_type: 通知类型
            streamer_name: 主播名称
            platform_name: 平台名称
            **kwargs: 其他参数

        Returns:
            格式化后的消息
        """
        if notification_type == 'live_start':
            title = kwargs.get('title', '')
            live_url = kwargs.get('live_url', '')
            return f"🔴 【{platform_name}】{streamer_name} 开播了！\n\n📺 {title}\n🔗 {live_url}\n\n快去围观吧！"
        
        elif notification_type == 'live_end':
            return f"⚫ 【{platform_name}】{streamer_name} 下播了\n\n感谢观看！"
        
        elif notification_type == 'title_change':
            title = kwargs.get('title', '')
            live_url = kwargs.get('live_url', '')
            return f"📝 【{platform_name}】{streamer_name} 更新了直播标题\n\n新标题：{title}\n🔗 {live_url}"
        
        else:
            return f"📊 【{platform_name}】{streamer_name} 状态更新"
