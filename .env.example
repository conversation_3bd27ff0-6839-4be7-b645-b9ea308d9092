# 应用配置
APP_NAME=直播监控系统
APP_VERSION=1.0.0
APP_DEBUG=true
APP_HOST=0.0.0.0
APP_PORT=8000

# 数据库配置
DATABASE_URL=postgresql://postgres:password@localhost:5432/live_monitor
DATABASE_ECHO=true

# Redis配置
REDIS_URL=redis://localhost:6379/0

# 安全配置
SECRET_KEY=your-super-secret-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 监控配置
MONITOR_CHECK_INTERVAL=30
MONITOR_MAX_CONCURRENT_CHECKS=10
MONITOR_RETRY_ATTEMPTS=3
MONITOR_RETRY_DELAY=5

# 通知配置 - WxPusher
WXPUSHER_APP_TOKEN=AT_xxxxxxxxxxxxxxxxxxxxxxxxxxxxx
WXPUSHER_BASE_URL=https://wxpusher.zjiecode.com/api

# 通知配置 - 邮件
EMAIL_SMTP_SERVER=smtp.gmail.com
EMAIL_SMTP_PORT=587
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your-app-password

# 平台配置 - B站
BILIBILI_BASE_URL=https://api.live.bilibili.com
BILIBILI_RATE_LIMIT=60

# 平台配置 - 斗鱼
DOUYU_BASE_URL=https://open.douyucdn.cn/api
DOUYU_RATE_LIMIT=60

# 平台配置 - 虎牙
HUYA_BASE_URL=https://mp.huya.com
HUYA_RATE_LIMIT=60

# 日志配置
LOG_LEVEL=DEBUG
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s
LOG_FILE=logs/app.log
LOG_MAX_BYTES=10485760
LOG_BACKUP_COUNT=5
