import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  notificationsApi,
  type NotificationPreferences,
  type NotificationHistory,
  type NotificationChannel,
  type NotificationStats,
  type TestNotificationRequest
} from '@/api/notifications'

export const useNotificationsStore = defineStore('notifications', () => {
  // 状态
  const preferences = ref<NotificationPreferences>({
    notify_live_start: true,
    notify_live_end: true,
    notify_title_change: true,
    enabled_channels: ['wxpusher'],
    quiet_hours: {
      enabled: false,
      start_time: '22:00',
      end_time: '08:00'
    }
  })
  
  const history = ref<NotificationHistory[]>([])
  const channels = ref<NotificationChannel[]>([])
  const stats = ref<NotificationStats | null>(null)
  const loading = ref(false)
  const historyLoading = ref(false)

  // 计算属性
  const enabledChannels = computed(() => 
    channels.value.filter(channel => channel.enabled)
  )

  const recentNotifications = computed(() => 
    history.value.slice(0, 10)
  )

  const notificationsByType = computed(() => {
    const grouped: Record<string, NotificationHistory[]> = {}
    history.value.forEach(notification => {
      const type = notification.notification_type
      if (!grouped[type]) {
        grouped[type] = []
      }
      grouped[type].push(notification)
    })
    return grouped
  })

  const successRate = computed(() => {
    if (history.value.length === 0) return 0
    const sentCount = history.value.filter(n => n.status === 'sent').length
    return Math.round((sentCount / history.value.length) * 100)
  })

  // 获取通知偏好设置
  const fetchPreferences = async () => {
    try {
      loading.value = true
      const data = await notificationsApi.getPreferences()
      preferences.value = data
    } catch (error) {
      console.error('获取通知偏好失败:', error)
      ElMessage.error('获取通知偏好失败')
    } finally {
      loading.value = false
    }
  }

  // 更新通知偏好设置
  const updatePreferences = async (newPreferences: NotificationPreferences) => {
    try {
      loading.value = true
      await notificationsApi.updatePreferences(newPreferences)
      preferences.value = newPreferences
      ElMessage.success('通知偏好设置已更新')
    } catch (error) {
      console.error('更新通知偏好失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取通知历史
  const fetchHistory = async (params?: {
    limit?: number
    offset?: number
    notification_type?: string
    channel?: string
    status?: string
  }) => {
    try {
      historyLoading.value = true
      const data = await notificationsApi.getHistory(params)
      
      if (params?.offset && params.offset > 0) {
        // 追加数据（分页加载）
        history.value.push(...data)
      } else {
        // 替换数据（首次加载或刷新）
        history.value = data
      }
    } catch (error) {
      console.error('获取通知历史失败:', error)
      ElMessage.error('获取通知历史失败')
    } finally {
      historyLoading.value = false
    }
  }

  // 发送测试通知
  const sendTestNotification = async (request: TestNotificationRequest) => {
    try {
      await notificationsApi.sendTestNotification(request)
      ElMessage.success('测试通知发送成功')
      
      // 刷新通知历史
      await fetchHistory({ limit: 20 })
    } catch (error) {
      console.error('发送测试通知失败:', error)
      throw error
    }
  }

  // 获取可用通知渠道
  const fetchChannels = async () => {
    try {
      const data = await notificationsApi.getAvailableChannels()
      channels.value = data.channels
    } catch (error) {
      console.error('获取通知渠道失败:', error)
      ElMessage.error('获取通知渠道失败')
    }
  }

  // 获取通知统计
  const fetchStats = async (days: number = 7) => {
    try {
      const data = await notificationsApi.getStats({ days })
      stats.value = data
    } catch (error) {
      console.error('获取通知统计失败:', error)
      ElMessage.error('获取通知统计失败')
    }
  }

  // 清空通知历史
  const clearHistory = () => {
    history.value = []
  }

  // 根据ID获取通知
  const getNotificationById = (id: string) => {
    return history.value.find(notification => notification.id === id)
  }

  // 获取指定类型的通知
  const getNotificationsByType = (type: string) => {
    return history.value.filter(notification => notification.notification_type === type)
  }

  // 获取指定渠道的通知
  const getNotificationsByChannel = (channel: string) => {
    return history.value.filter(notification => notification.channel === channel)
  }

  // 获取失败的通知
  const getFailedNotifications = () => {
    return history.value.filter(notification => notification.status === 'failed')
  }

  // 检查渠道是否启用
  const isChannelEnabled = (channelName: string) => {
    return preferences.value.enabled_channels.includes(channelName)
  }

  // 检查是否在免打扰时段
  const isInQuietHours = () => {
    if (!preferences.value.quiet_hours?.enabled) {
      return false
    }

    const now = new Date()
    const currentTime = now.getHours() * 60 + now.getMinutes()
    
    const startTime = preferences.value.quiet_hours.start_time.split(':')
    const endTime = preferences.value.quiet_hours.end_time.split(':')
    
    const startMinutes = parseInt(startTime[0]) * 60 + parseInt(startTime[1])
    const endMinutes = parseInt(endTime[0]) * 60 + parseInt(endTime[1])
    
    if (startMinutes <= endMinutes) {
      return currentTime >= startMinutes && currentTime <= endMinutes
    } else {
      // 跨天的情况
      return currentTime >= startMinutes || currentTime <= endMinutes
    }
  }

  return {
    // 状态
    preferences: readonly(preferences),
    history: readonly(history),
    channels: readonly(channels),
    stats: readonly(stats),
    loading: readonly(loading),
    historyLoading: readonly(historyLoading),
    
    // 计算属性
    enabledChannels,
    recentNotifications,
    notificationsByType,
    successRate,
    
    // 方法
    fetchPreferences,
    updatePreferences,
    fetchHistory,
    sendTestNotification,
    fetchChannels,
    fetchStats,
    clearHistory,
    getNotificationById,
    getNotificationsByType,
    getNotificationsByChannel,
    getFailedNotifications,
    isChannelEnabled,
    isInQuietHours
  }
})
