"""缓存工具模块"""
import json
import pickle
from typing import Any, Optional, Union
from datetime import datetime, timedelta

import aioredis
from aioredis import Redis

from .config import get_settings
from .logger import get_logger

logger = get_logger("cache")


class CacheService:
    """缓存服务"""
    
    def __init__(self, redis_url: Optional[str] = None):
        """
        Args:
            redis_url: Redis连接URL
        """
        settings = get_settings()
        self.redis_url = redis_url or settings.redis.url
        self.redis: Optional[Redis] = None
    
    async def connect(self) -> None:
        """连接Redis"""
        if self.redis is None:
            try:
                self.redis = await aioredis.from_url(
                    self.redis_url,
                    encoding="utf-8",
                    decode_responses=True
                )
                logger.info("Redis连接成功")
            except Exception as e:
                logger.error(f"Redis连接失败: {e}")
                raise
    
    async def disconnect(self) -> None:
        """断开Redis连接"""
        if self.redis:
            await self.redis.close()
            self.redis = None
            logger.info("Redis连接已关闭")
    
    async def ping(self) -> bool:
        """检查Redis连接"""
        try:
            if not self.redis:
                await self.connect()
            await self.redis.ping()
            return True
        except Exception as e:
            logger.error(f"Redis ping失败: {e}")
            return False
    
    def _serialize_value(self, value: Any) -> str:
        """序列化值"""
        if isinstance(value, (str, int, float, bool)):
            return json.dumps(value)
        else:
            # 对于复杂对象，使用pickle序列化后再base64编码
            import base64
            pickled = pickle.dumps(value)
            encoded = base64.b64encode(pickled).decode('utf-8')
            return json.dumps({"__pickle__": encoded})
    
    def _deserialize_value(self, value: str) -> Any:
        """反序列化值"""
        try:
            data = json.loads(value)
            if isinstance(data, dict) and "__pickle__" in data:
                # 反序列化pickle对象
                import base64
                pickled = base64.b64decode(data["__pickle__"].encode('utf-8'))
                return pickle.loads(pickled)
            return data
        except (json.JSONDecodeError, pickle.PickleError) as e:
            logger.error(f"反序列化失败: {e}")
            return None
    
    async def set(
        self,
        key: str,
        value: Any,
        ttl: Optional[int] = None
    ) -> bool:
        """设置缓存
        
        Args:
            key: 缓存键
            value: 缓存值
            ttl: 过期时间（秒）
        
        Returns:
            是否设置成功
        """
        try:
            if not self.redis:
                await self.connect()
            
            serialized_value = self._serialize_value(value)
            
            if ttl:
                await self.redis.setex(key, ttl, serialized_value)
            else:
                await self.redis.set(key, serialized_value)
            
            logger.debug(f"缓存设置成功: {key}")
            return True
        
        except Exception as e:
            logger.error(f"缓存设置失败: {key}, 错误: {e}")
            return False
    
    async def get(self, key: str) -> Any:
        """获取缓存
        
        Args:
            key: 缓存键
        
        Returns:
            缓存值，不存在返回None
        """
        try:
            if not self.redis:
                await self.connect()
            
            value = await self.redis.get(key)
            if value is None:
                return None
            
            result = self._deserialize_value(value)
            logger.debug(f"缓存获取成功: {key}")
            return result
        
        except Exception as e:
            logger.error(f"缓存获取失败: {key}, 错误: {e}")
            return None
    
    async def delete(self, key: str) -> bool:
        """删除缓存
        
        Args:
            key: 缓存键
        
        Returns:
            是否删除成功
        """
        try:
            if not self.redis:
                await self.connect()
            
            result = await self.redis.delete(key)
            logger.debug(f"缓存删除: {key}, 结果: {result}")
            return result > 0
        
        except Exception as e:
            logger.error(f"缓存删除失败: {key}, 错误: {e}")
            return False
    
    async def exists(self, key: str) -> bool:
        """检查缓存是否存在
        
        Args:
            key: 缓存键
        
        Returns:
            是否存在
        """
        try:
            if not self.redis:
                await self.connect()
            
            result = await self.redis.exists(key)
            return result > 0
        
        except Exception as e:
            logger.error(f"缓存检查失败: {key}, 错误: {e}")
            return False
    
    async def expire(self, key: str, ttl: int) -> bool:
        """设置缓存过期时间
        
        Args:
            key: 缓存键
            ttl: 过期时间（秒）
        
        Returns:
            是否设置成功
        """
        try:
            if not self.redis:
                await self.connect()
            
            result = await self.redis.expire(key, ttl)
            logger.debug(f"缓存过期时间设置: {key}, TTL: {ttl}, 结果: {result}")
            return result
        
        except Exception as e:
            logger.error(f"缓存过期时间设置失败: {key}, 错误: {e}")
            return False
    
    async def ttl(self, key: str) -> int:
        """获取缓存剩余过期时间
        
        Args:
            key: 缓存键
        
        Returns:
            剩余时间（秒），-1表示永不过期，-2表示不存在
        """
        try:
            if not self.redis:
                await self.connect()
            
            result = await self.redis.ttl(key)
            return result
        
        except Exception as e:
            logger.error(f"获取缓存TTL失败: {key}, 错误: {e}")
            return -2
    
    async def keys(self, pattern: str = "*") -> list:
        """获取匹配的缓存键
        
        Args:
            pattern: 匹配模式
        
        Returns:
            匹配的键列表
        """
        try:
            if not self.redis:
                await self.connect()
            
            keys = await self.redis.keys(pattern)
            return keys
        
        except Exception as e:
            logger.error(f"获取缓存键失败: {pattern}, 错误: {e}")
            return []
    
    async def clear_pattern(self, pattern: str) -> int:
        """清除匹配模式的缓存
        
        Args:
            pattern: 匹配模式
        
        Returns:
            删除的键数量
        """
        try:
            keys = await self.keys(pattern)
            if keys:
                result = await self.redis.delete(*keys)
                logger.info(f"清除缓存: 模式={pattern}, 删除数量={result}")
                return result
            return 0
        
        except Exception as e:
            logger.error(f"清除缓存失败: {pattern}, 错误: {e}")
            return 0


# 全局缓存服务实例
cache_service = CacheService()


def cache_key(*parts: str) -> str:
    """生成缓存键
    
    Args:
        *parts: 键的组成部分
    
    Returns:
        缓存键
    """
    return ":".join(str(part) for part in parts)


def streamer_cache_key(platform: str, streamer_id: str) -> str:
    """生成主播缓存键"""
    return cache_key("streamer", platform, streamer_id)


def user_cache_key(user_id: str) -> str:
    """生成用户缓存键"""
    return cache_key("user", user_id)
