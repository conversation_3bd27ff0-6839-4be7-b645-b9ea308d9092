import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  streamersApi, 
  type Streamer, 
  type SearchStreamerResult, 
  type AddStreamerRequest,
  type SubscriptionSettings 
} from '@/api/streamers'

export const useStreamersStore = defineStore('streamers', () => {
  // 状态
  const streamers = ref<Streamer[]>([])
  const searchResults = ref<SearchStreamerResult[]>([])
  const loading = ref(false)
  const searchLoading = ref(false)

  // 计算属性
  const liveStreamers = computed(() => 
    streamers.value.filter(streamer => streamer.is_live)
  )

  const offlineStreamers = computed(() => 
    streamers.value.filter(streamer => !streamer.is_live)
  )

  const streamersByPlatform = computed(() => {
    const grouped: Record<string, Streamer[]> = {}
    streamers.value.forEach(streamer => {
      if (!grouped[streamer.platform]) {
        grouped[streamer.platform] = []
      }
      grouped[streamer.platform].push(streamer)
    })
    return grouped
  })

  const totalStreamers = computed(() => streamers.value.length)
  const liveCount = computed(() => liveStreamers.value.length)
  const offlineCount = computed(() => offlineStreamers.value.length)

  // 获取主播列表
  const fetchStreamers = async (filters?: { platform?: string; is_live?: boolean }) => {
    try {
      loading.value = true
      const data = await streamersApi.getStreamers(filters)
      streamers.value = data
    } catch (error) {
      console.error('获取主播列表失败:', error)
      ElMessage.error('获取主播列表失败')
    } finally {
      loading.value = false
    }
  }

  // 添加主播
  const addStreamer = async (streamerData: AddStreamerRequest) => {
    try {
      const response = await streamersApi.addStreamer(streamerData)
      
      // 添加到本地列表
      streamers.value.push(response.streamer)
      
      ElMessage.success('主播添加成功')
      return response
    } catch (error) {
      console.error('添加主播失败:', error)
      throw error
    }
  }

  // 删除主播
  const removeStreamer = async (streamerId: string) => {
    try {
      await streamersApi.removeStreamer(streamerId)
      
      // 从本地列表移除
      const index = streamers.value.findIndex(s => s.id === streamerId)
      if (index > -1) {
        streamers.value.splice(index, 1)
      }
      
      ElMessage.success('主播删除成功')
    } catch (error) {
      console.error('删除主播失败:', error)
      throw error
    }
  }

  // 更新主播设置
  const updateStreamerSettings = async (streamerId: string, settings: SubscriptionSettings) => {
    try {
      await streamersApi.updateStreamerSettings(streamerId, settings)
      
      ElMessage.success('设置更新成功')
    } catch (error) {
      console.error('更新主播设置失败:', error)
      throw error
    }
  }

  // 搜索主播
  const searchStreamers = async (platform: string, keyword: string) => {
    try {
      searchLoading.value = true
      const results = await streamersApi.searchStreamers({ platform, keyword })
      searchResults.value = results
      return results
    } catch (error) {
      console.error('搜索主播失败:', error)
      ElMessage.error('搜索主播失败')
      return []
    } finally {
      searchLoading.value = false
    }
  }

  // 清空搜索结果
  const clearSearchResults = () => {
    searchResults.value = []
  }

  // 根据ID获取主播
  const getStreamerById = (id: string) => {
    return streamers.value.find(streamer => streamer.id === id)
  }

  // 根据平台和主播ID获取主播
  const getStreamerByPlatformId = (platform: string, streamerId: string) => {
    return streamers.value.find(
      streamer => streamer.platform === platform && streamer.streamer_id === streamerId
    )
  }

  // 更新主播状态（用于实时更新）
  const updateStreamerStatus = (streamerId: string, updates: Partial<Streamer>) => {
    const index = streamers.value.findIndex(s => s.id === streamerId)
    if (index > -1) {
      streamers.value[index] = { ...streamers.value[index], ...updates }
    }
  }

  // 刷新单个主播状态
  const refreshStreamer = async (streamerId: string) => {
    try {
      // 重新获取主播列表来更新状态
      await fetchStreamers()
    } catch (error) {
      console.error('刷新主播状态失败:', error)
    }
  }

  // 批量刷新所有主播状态
  const refreshAllStreamers = async () => {
    await fetchStreamers()
  }

  return {
    // 状态
    streamers: readonly(streamers),
    searchResults: readonly(searchResults),
    loading: readonly(loading),
    searchLoading: readonly(searchLoading),
    
    // 计算属性
    liveStreamers,
    offlineStreamers,
    streamersByPlatform,
    totalStreamers,
    liveCount,
    offlineCount,
    
    // 方法
    fetchStreamers,
    addStreamer,
    removeStreamer,
    updateStreamerSettings,
    searchStreamers,
    clearSearchResults,
    getStreamerById,
    getStreamerByPlatformId,
    updateStreamerStatus,
    refreshStreamer,
    refreshAllStreamers
  }
})
