import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { authApi, type User, type LoginRequest, type RegisterRequest } from '@/api/auth'
import router from '@/router'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const token = ref<string | null>(localStorage.getItem('token'))
  const user = ref<User | null>(null)
  const loading = ref(false)

  // 计算属性
  const isAuthenticated = computed(() => !!token.value && !!user.value)

  // 登录
  const login = async (credentials: LoginRequest) => {
    try {
      loading.value = true
      const response = await authApi.login(credentials)
      
      // 保存token和用户信息
      token.value = response.access_token
      user.value = response.user
      localStorage.setItem('token', response.access_token)
      
      ElMessage.success('登录成功')
      
      // 重定向到目标页面或仪表板
      const redirect = router.currentRoute.value.query.redirect as string
      await router.push(redirect || '/dashboard')
      
      return response
    } catch (error) {
      console.error('登录失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 注册
  const register = async (userData: RegisterRequest) => {
    try {
      loading.value = true
      const response = await authApi.register(userData)
      
      // 注册成功后自动登录
      token.value = response.access_token
      user.value = response.user
      localStorage.setItem('token', response.access_token)
      
      ElMessage.success('注册成功')
      await router.push('/dashboard')
      
      return response
    } catch (error) {
      console.error('注册失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 登出
  const logout = async () => {
    try {
      // 调用后端登出接口
      if (token.value) {
        await authApi.logout()
      }
    } catch (error) {
      console.error('登出请求失败:', error)
    } finally {
      // 清除本地状态
      token.value = null
      user.value = null
      localStorage.removeItem('token')
      
      ElMessage.success('已退出登录')
      await router.push('/login')
    }
  }

  // 检查认证状态
  const checkAuth = async () => {
    if (!token.value) {
      return false
    }

    try {
      const response = await authApi.verifyToken()
      if (response.valid) {
        user.value = response.user
        return true
      } else {
        // token无效，清除本地状态
        token.value = null
        user.value = null
        localStorage.removeItem('token')
        return false
      }
    } catch (error) {
      console.error('验证token失败:', error)
      // token验证失败，清除本地状态
      token.value = null
      user.value = null
      localStorage.removeItem('token')
      return false
    }
  }

  // 获取用户信息
  const fetchUserInfo = async () => {
    try {
      const userInfo = await authApi.getCurrentUser()
      user.value = userInfo
      return userInfo
    } catch (error) {
      console.error('获取用户信息失败:', error)
      throw error
    }
  }

  // 更新用户信息
  const updateUser = (userData: Partial<User>) => {
    if (user.value) {
      user.value = { ...user.value, ...userData }
    }
  }

  // 修改密码
  const changePassword = async (oldPassword: string, newPassword: string) => {
    try {
      loading.value = true
      await authApi.changePassword({
        old_password: oldPassword,
        new_password: newPassword
      })
      
      ElMessage.success('密码修改成功')
    } catch (error) {
      console.error('修改密码失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  return {
    // 状态
    token: readonly(token),
    user: readonly(user),
    loading: readonly(loading),
    
    // 计算属性
    isAuthenticated,
    
    // 方法
    login,
    register,
    logout,
    checkAuth,
    fetchUserInfo,
    updateUser,
    changePassword
  }
})
