"""API模块"""

from fastapi import APIRouter
from .auth import router as auth_router
from .monitors import router as monitors_router
from .notifications import router as notifications_router

# 创建主路由器
api_router = APIRouter()

# 注册子路由
api_router.include_router(auth_router)
api_router.include_router(monitors_router)
api_router.include_router(notifications_router)

__all__ = [
    "api_router",
    "auth_router",
    "monitors_router", 
    "notifications_router"
]
