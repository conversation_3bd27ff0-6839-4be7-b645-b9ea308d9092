"""HTTP客户端工具模块"""
import asyncio
import time
from typing import Dict, Any, Optional, Union
from urllib.parse import urljoin

import aiohttp
from aiohttp import ClientTimeout, ClientSession

from .logger import get_logger

logger = get_logger("http_client")


class RateLimiter:
    """速率限制器"""
    
    def __init__(self, max_requests: int, time_window: int = 60):
        """
        Args:
            max_requests: 时间窗口内最大请求数
            time_window: 时间窗口（秒）
        """
        self.max_requests = max_requests
        self.time_window = time_window
        self.requests = []
        self._lock = asyncio.Lock()
    
    async def acquire(self) -> None:
        """获取请求许可"""
        async with self._lock:
            now = time.time()
            
            # 清理过期的请求记录
            self.requests = [req_time for req_time in self.requests 
                           if now - req_time < self.time_window]
            
            # 检查是否超过限制
            if len(self.requests) >= self.max_requests:
                # 计算需要等待的时间
                oldest_request = min(self.requests)
                wait_time = self.time_window - (now - oldest_request)
                
                if wait_time > 0:
                    logger.warning(f"达到速率限制，等待 {wait_time:.2f} 秒")
                    await asyncio.sleep(wait_time)
                    return await self.acquire()
            
            # 记录当前请求
            self.requests.append(now)


class HTTPClient:
    """HTTP客户端"""
    
    def __init__(
        self,
        base_url: str = "",
        timeout: int = 10,
        rate_limit: Optional[int] = None,
        headers: Optional[Dict[str, str]] = None
    ):
        """
        Args:
            base_url: 基础URL
            timeout: 超时时间（秒）
            rate_limit: 速率限制（每分钟请求数）
            headers: 默认请求头
        """
        self.base_url = base_url
        self.timeout = ClientTimeout(total=timeout)
        self.default_headers = headers or {}
        self.session: Optional[ClientSession] = None
        
        # 设置速率限制器
        self.rate_limiter = RateLimiter(rate_limit) if rate_limit else None
        
        # 默认请求头
        self.default_headers.setdefault("User-Agent", "LiveMonitor/1.0")
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.start()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close()
    
    async def start(self) -> None:
        """启动HTTP客户端"""
        if self.session is None:
            self.session = ClientSession(
                timeout=self.timeout,
                headers=self.default_headers
            )
    
    async def close(self) -> None:
        """关闭HTTP客户端"""
        if self.session:
            await self.session.close()
            self.session = None
    
    def _build_url(self, url: str) -> str:
        """构建完整URL"""
        if url.startswith(("http://", "https://")):
            return url
        return urljoin(self.base_url, url)
    
    async def _make_request(
        self,
        method: str,
        url: str,
        params: Optional[Dict[str, Any]] = None,
        data: Optional[Union[Dict[str, Any], str]] = None,
        json: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """发起HTTP请求"""
        if not self.session:
            await self.start()
        
        # 应用速率限制
        if self.rate_limiter:
            await self.rate_limiter.acquire()
        
        full_url = self._build_url(url)
        request_headers = {**self.default_headers}
        if headers:
            request_headers.update(headers)
        
        logger.debug(f"发起 {method} 请求: {full_url}")
        
        try:
            async with self.session.request(
                method=method,
                url=full_url,
                params=params,
                data=data,
                json=json,
                headers=request_headers,
                **kwargs
            ) as response:
                response.raise_for_status()
                
                # 尝试解析JSON响应
                try:
                    result = await response.json()
                    logger.debug(f"请求成功: {method} {full_url}, 状态码: {response.status}")
                    return result
                except aiohttp.ContentTypeError:
                    # 如果不是JSON，返回文本
                    text = await response.text()
                    logger.debug(f"请求成功: {method} {full_url}, 状态码: {response.status}, 响应: {text[:100]}...")
                    return {"text": text}
        
        except aiohttp.ClientError as e:
            logger.error(f"请求失败: {method} {full_url}, 错误: {e}")
            raise
        except Exception as e:
            logger.error(f"请求异常: {method} {full_url}, 错误: {e}")
            raise
    
    async def get(
        self,
        url: str,
        params: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """GET请求"""
        return await self._make_request("GET", url, params=params, headers=headers, **kwargs)
    
    async def post(
        self,
        url: str,
        data: Optional[Union[Dict[str, Any], str]] = None,
        json: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """POST请求"""
        return await self._make_request("POST", url, data=data, json=json, headers=headers, **kwargs)
    
    async def put(
        self,
        url: str,
        data: Optional[Union[Dict[str, Any], str]] = None,
        json: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """PUT请求"""
        return await self._make_request("PUT", url, data=data, json=json, headers=headers, **kwargs)
    
    async def delete(
        self,
        url: str,
        headers: Optional[Dict[str, str]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """DELETE请求"""
        return await self._make_request("DELETE", url, headers=headers, **kwargs)


class HTTPClientManager:
    """HTTP客户端管理器"""
    
    def __init__(self):
        self.clients: Dict[str, HTTPClient] = {}
    
    def get_client(
        self,
        name: str,
        base_url: str = "",
        timeout: int = 10,
        rate_limit: Optional[int] = None,
        headers: Optional[Dict[str, str]] = None
    ) -> HTTPClient:
        """获取或创建HTTP客户端"""
        if name not in self.clients:
            self.clients[name] = HTTPClient(
                base_url=base_url,
                timeout=timeout,
                rate_limit=rate_limit,
                headers=headers
            )
        return self.clients[name]
    
    async def close_all(self) -> None:
        """关闭所有客户端"""
        for client in self.clients.values():
            await client.close()
        self.clients.clear()


# 全局HTTP客户端管理器
http_client_manager = HTTPClientManager()
