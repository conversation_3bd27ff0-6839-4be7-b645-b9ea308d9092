"""订阅模型"""
import json
import uuid
from datetime import datetime
from typing import Dict, Any, Optional

from sqlalchemy import Column, String, DateTime, Boolean, Text, ForeignKey, UniqueConstraint
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from ..database import Base


class Subscription(Base):
    """订阅模型"""
    __tablename__ = 'subscriptions'

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=False, index=True)
    streamer_id = Column(UUID(as_uuid=True), ForeignKey('streamers.id'), nullable=False, index=True)
    is_active = Column(Boolean, default=True, index=True)
    created_at = Column(DateTime, default=datetime.utcnow)

    # 个性化设置
    custom_settings = Column(Text)  # JSON字符串，存储个性化通知设置

    # 关系
    user = relationship("User", back_populates="subscriptions")
    streamer = relationship("Streamer", back_populates="subscriptions")

    # 唯一约束：用户不能重复订阅同一主播
    __table_args__ = (
        UniqueConstraint('user_id', 'streamer_id', name='uq_user_streamer'),
    )

    def __repr__(self) -> str:
        return f"<Subscription(id={self.id}, user_id={self.user_id}, streamer_id={self.streamer_id}, active={self.is_active})>"

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': str(self.id),
            'user_id': str(self.user_id),
            'streamer_id': str(self.streamer_id),
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'custom_settings': self.get_custom_settings()
        }

    def get_custom_settings(self) -> Dict[str, Any]:
        """获取个性化设置"""
        if self.custom_settings:
            try:
                return json.loads(self.custom_settings)
            except json.JSONDecodeError:
                pass
        
        # 返回默认设置
        return {
            'notify_live_start': True,
            'notify_live_end': True,
            'notify_title_change': True,
            'custom_message_template': None,
            'priority': 'normal'  # normal, high, low
        }

    def set_custom_settings(self, settings: Dict[str, Any]) -> None:
        """设置个性化设置"""
        self.custom_settings = json.dumps(settings, ensure_ascii=False)

    def update_custom_settings(self, updates: Dict[str, Any]) -> None:
        """更新个性化设置"""
        current_settings = self.get_custom_settings()
        current_settings.update(updates)
        self.set_custom_settings(current_settings)

    def is_notification_enabled(self, notification_type: str) -> bool:
        """检查是否启用了指定类型的通知"""
        settings = self.get_custom_settings()
        type_mapping = {
            'live_start': 'notify_live_start',
            'live_end': 'notify_live_end',
            'title_change': 'notify_title_change'
        }
        
        pref_key = type_mapping.get(notification_type)
        if pref_key:
            return settings.get(pref_key, True)
        
        return True

    def get_priority(self) -> str:
        """获取通知优先级"""
        settings = self.get_custom_settings()
        return settings.get('priority', 'normal')

    def get_custom_message_template(self) -> Optional[str]:
        """获取自定义消息模板"""
        settings = self.get_custom_settings()
        return settings.get('custom_message_template')

    def activate(self) -> None:
        """激活订阅"""
        self.is_active = True

    def deactivate(self) -> None:
        """停用订阅"""
        self.is_active = False

    def to_detailed_dict(self) -> Dict[str, Any]:
        """转换为包含详细信息的字典"""
        result = self.to_dict()
        
        # 添加主播信息
        if self.streamer:
            result['streamer'] = self.streamer.to_status_dict()
        
        # 添加用户信息（不包含敏感信息）
        if self.user:
            result['user'] = {
                'id': str(self.user.id),
                'username': self.user.username
            }
        
        return result
