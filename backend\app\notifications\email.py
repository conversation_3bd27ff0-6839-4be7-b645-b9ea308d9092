"""邮件通知渠道"""
import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.header import Head<PERSON>
from typing import Optional

from .base import BaseNotificationChannel
from ..utils import get_settings, get_logger

logger = get_logger("email")


class EmailChannel(BaseNotificationChannel):
    """邮件通知渠道"""

    def __init__(self):
        super().__init__("email")
        self.settings = get_settings()
        self.smtp_server = self.settings.notifications.email_smtp_server
        self.smtp_port = self.settings.notifications.email_smtp_port
        self.username = self.settings.notifications.email_username
        self.password = self.settings.notifications.email_password

    async def send_message(self, user_id: str, message: str, **kwargs) -> bool:
        """
        发送邮件消息

        Args:
            user_id: 用户邮箱地址
            message: 消息内容
            **kwargs: 额外参数
                - subject: 邮件主题
                - html_content: HTML内容
                - from_name: 发件人名称

        Returns:
            是否发送成功
        """
        try:
            # 创建邮件对象
            msg = MIMEMultipart('alternative')
            
            # 设置邮件头
            subject = kwargs.get("subject", "直播监控通知")
            from_name = kwargs.get("from_name", "直播监控系统")
            
            msg['Subject'] = Header(subject, 'utf-8')
            msg['From'] = f"{from_name} <{self.username}>"
            msg['To'] = user_id

            # 添加文本内容
            text_part = MIMEText(message, 'plain', 'utf-8')
            msg.attach(text_part)

            # 如果有HTML内容，添加HTML部分
            html_content = kwargs.get("html_content")
            if html_content:
                html_part = MIMEText(html_content, 'html', 'utf-8')
                msg.attach(html_part)

            # 发送邮件
            success = await self._send_email(msg, user_id)
            
            if success:
                logger.info(f"邮件发送成功: {user_id}")
            else:
                logger.error(f"邮件发送失败: {user_id}")
            
            return success

        except Exception as e:
            logger.error(f"邮件发送异常: {user_id}, 错误: {e}")
            return False

    async def _send_email(self, msg: MIMEMultipart, to_email: str) -> bool:
        """发送邮件"""
        try:
            # 创建SSL上下文
            context = ssl.create_default_context()
            
            # 连接SMTP服务器
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                server.starttls(context=context)
                server.login(self.username, self.password)
                
                # 发送邮件
                text = msg.as_string()
                server.sendmail(self.username, to_email, text)
                
                return True

        except Exception as e:
            logger.error(f"SMTP发送失败: {e}")
            return False

    async def validate_config(self) -> bool:
        """验证邮件配置"""
        if not all([self.smtp_server, self.smtp_port, self.username, self.password]):
            logger.error("邮件配置不完整")
            return False

        try:
            # 测试SMTP连接
            context = ssl.create_default_context()
            
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                server.starttls(context=context)
                server.login(self.username, self.password)
                
                logger.info("邮件配置验证成功")
                return True

        except Exception as e:
            logger.error(f"邮件配置验证失败: {e}")
            return False

    async def send_streamer_notification(
        self,
        user_email: str,
        notification_type: str,
        streamer_name: str,
        platform_name: str,
        **kwargs
    ) -> bool:
        """
        发送主播通知邮件

        Args:
            user_email: 用户邮箱
            notification_type: 通知类型
            streamer_name: 主播名称
            platform_name: 平台名称
            **kwargs: 其他参数

        Returns:
            是否发送成功
        """
        # 生成邮件主题
        if notification_type == 'live_start':
            subject = f"【{platform_name}】{streamer_name} 开播了！"
        elif notification_type == 'live_end':
            subject = f"【{platform_name}】{streamer_name} 下播了"
        elif notification_type == 'title_change':
            subject = f"【{platform_name}】{streamer_name} 更新了标题"
        else:
            subject = f"【{platform_name}】{streamer_name} 状态更新"

        # 生成邮件内容
        message = self.format_message(notification_type, streamer_name, platform_name, **kwargs)
        
        # 生成HTML内容
        html_content = self._generate_html_content(
            notification_type, streamer_name, platform_name, **kwargs
        )

        # 发送邮件
        return await self.send_message(
            user_email,
            message,
            subject=subject,
            html_content=html_content
        )

    def _generate_html_content(
        self,
        notification_type: str,
        streamer_name: str,
        platform_name: str,
        **kwargs
    ) -> str:
        """生成HTML邮件内容"""
        title = kwargs.get('title', '')
        live_url = kwargs.get('live_url', '')
        cover_image = kwargs.get('cover_image', '')
        
        if notification_type == 'live_start':
            html = f"""
            <html>
            <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                    <h2 style="color: #e74c3c;">🔴 {streamer_name} 开播了！</h2>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;">
                        <p><strong>平台：</strong>{platform_name}</p>
                        <p><strong>主播：</strong>{streamer_name}</p>
                        <p><strong>标题：</strong>{title}</p>
                    </div>
                    {f'<img src="{cover_image}" style="max-width: 100%; height: auto; border-radius: 5px;" alt="直播封面">' if cover_image else ''}
                    <div style="text-align: center; margin: 20px 0;">
                        <a href="{live_url}" style="background: #3498db; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;">
                            立即观看
                        </a>
                    </div>
                    <p style="color: #666; font-size: 12px; text-align: center;">
                        此邮件由直播监控系统自动发送
                    </p>
                </div>
            </body>
            </html>
            """
        elif notification_type == 'live_end':
            html = f"""
            <html>
            <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                    <h2 style="color: #95a5a6;">⚫ {streamer_name} 下播了</h2>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;">
                        <p><strong>平台：</strong>{platform_name}</p>
                        <p><strong>主播：</strong>{streamer_name}</p>
                    </div>
                    <p style="text-align: center; color: #666;">感谢观看！</p>
                    <p style="color: #666; font-size: 12px; text-align: center;">
                        此邮件由直播监控系统自动发送
                    </p>
                </div>
            </body>
            </html>
            """
        else:
            html = f"""
            <html>
            <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                    <h2 style="color: #f39c12;">📝 {streamer_name} 更新了标题</h2>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;">
                        <p><strong>平台：</strong>{platform_name}</p>
                        <p><strong>主播：</strong>{streamer_name}</p>
                        <p><strong>新标题：</strong>{title}</p>
                    </div>
                    <div style="text-align: center; margin: 20px 0;">
                        <a href="{live_url}" style="background: #3498db; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;">
                            查看直播
                        </a>
                    </div>
                    <p style="color: #666; font-size: 12px; text-align: center;">
                        此邮件由直播监控系统自动发送
                    </p>
                </div>
            </body>
            </html>
            """
        
        return html
