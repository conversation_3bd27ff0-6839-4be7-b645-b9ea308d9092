"""平台适配器模块"""
from typing import Dict, Optional, List
from .base import BasePlatform
from .bilibili import BilibiliPlatform
from .douyu import DouyuPlatform
from .huya import HuyaPlatform
from ..utils import get_settings, get_logger

logger = get_logger("platforms")


class PlatformManager:
    """平台管理器"""
    
    def __init__(self):
        self.platforms: Dict[str, BasePlatform] = {}
        self._initialized = False
    
    async def initialize(self) -> None:
        """初始化所有平台适配器"""
        if self._initialized:
            return
        
        settings = get_settings()
        
        # 初始化B站适配器
        self.platforms['bilibili'] = BilibiliPlatform(
            base_url=settings.platforms.bilibili_base_url,
            rate_limit=settings.platforms.bilibili_rate_limit
        )
        
        # 初始化斗鱼适配器
        self.platforms['douyu'] = DouyuPlatform(
            base_url=settings.platforms.douyu_base_url,
            rate_limit=settings.platforms.douyu_rate_limit
        )
        
        # 初始化虎牙适配器
        self.platforms['huya'] = HuyaPlatform(
            base_url=settings.platforms.huya_base_url,
            rate_limit=settings.platforms.huya_rate_limit
        )
        
        # 启动所有平台适配器
        for platform_name, platform in self.platforms.items():
            try:
                await platform.start()
                logger.info(f"平台适配器启动成功: {platform_name}")
            except Exception as e:
                logger.error(f"平台适配器启动失败: {platform_name}, 错误: {e}")
        
        self._initialized = True
        logger.info("所有平台适配器初始化完成")
    
    async def close(self) -> None:
        """关闭所有平台适配器"""
        for platform_name, platform in self.platforms.items():
            try:
                await platform.close()
                logger.info(f"平台适配器关闭: {platform_name}")
            except Exception as e:
                logger.error(f"平台适配器关闭失败: {platform_name}, 错误: {e}")
        
        self.platforms.clear()
        self._initialized = False
        logger.info("所有平台适配器已关闭")
    
    def get_platform(self, platform_name: str) -> Optional[BasePlatform]:
        """获取平台适配器
        
        Args:
            platform_name: 平台名称
            
        Returns:
            平台适配器实例
        """
        return self.platforms.get(platform_name)
    
    def get_supported_platforms(self) -> List[str]:
        """获取支持的平台列表"""
        return list(self.platforms.keys())
    
    def is_platform_supported(self, platform_name: str) -> bool:
        """检查平台是否支持"""
        return platform_name in self.platforms
    
    async def validate_streamer(self, platform_name: str, streamer_id: str) -> bool:
        """验证主播是否存在
        
        Args:
            platform_name: 平台名称
            streamer_id: 主播ID
            
        Returns:
            是否存在
        """
        platform = self.get_platform(platform_name)
        if not platform:
            logger.warning(f"不支持的平台: {platform_name}")
            return False
        
        try:
            return await platform.validate_streamer_id(streamer_id)
        except Exception as e:
            logger.error(f"验证主播失败: {platform_name}:{streamer_id}, 错误: {e}")
            return False
    
    async def get_streamer_status(self, platform_name: str, streamer_id: str) -> Optional[dict]:
        """获取主播状态
        
        Args:
            platform_name: 平台名称
            streamer_id: 主播ID
            
        Returns:
            主播状态信息
        """
        platform = self.get_platform(platform_name)
        if not platform:
            logger.warning(f"不支持的平台: {platform_name}")
            return None
        
        try:
            return await platform.get_streamer_status(streamer_id)
        except Exception as e:
            logger.error(f"获取主播状态失败: {platform_name}:{streamer_id}, 错误: {e}")
            return None
    
    async def search_streamers(self, platform_name: str, keyword: str) -> List[dict]:
        """搜索主播
        
        Args:
            platform_name: 平台名称
            keyword: 搜索关键词
            
        Returns:
            搜索结果列表
        """
        platform = self.get_platform(platform_name)
        if not platform:
            logger.warning(f"不支持的平台: {platform_name}")
            return []
        
        try:
            return await platform.search_streamer(keyword)
        except Exception as e:
            logger.error(f"搜索主播失败: {platform_name}:{keyword}, 错误: {e}")
            return []
    
    def get_platform_display_name(self, platform_name: str) -> str:
        """获取平台显示名称"""
        platform_names = {
            'bilibili': 'B站',
            'douyu': '斗鱼',
            'huya': '虎牙'
        }
        return platform_names.get(platform_name, platform_name)


# 全局平台管理器实例
platform_manager = PlatformManager()


__all__ = [
    "BasePlatform",
    "BilibiliPlatform", 
    "DouyuPlatform",
    "HuyaPlatform",
    "PlatformManager",
    "platform_manager"
]
