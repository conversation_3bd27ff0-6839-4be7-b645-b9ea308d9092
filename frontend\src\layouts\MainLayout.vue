<template>
  <el-container class="main-layout">
    <!-- 侧边栏 -->
    <el-aside :width="isCollapse ? '64px' : '200px'" class="sidebar">
      <div class="logo">
        <el-icon v-if="isCollapse" size="24"><Monitor /></el-icon>
        <span v-else class="logo-text">直播监控</span>
      </div>
      
      <el-menu
        :default-active="activeMenu"
        :collapse="isCollapse"
        :unique-opened="true"
        router
        class="sidebar-menu"
      >
        <el-menu-item index="/dashboard">
          <el-icon><Monitor /></el-icon>
          <template #title>仪表板</template>
        </el-menu-item>
        
        <el-menu-item index="/streamers">
          <el-icon><User /></el-icon>
          <template #title>主播管理</template>
        </el-menu-item>
        
        <el-menu-item index="/streamers/add">
          <el-icon><Plus /></el-icon>
          <template #title>添加主播</template>
        </el-menu-item>
        
        <el-sub-menu index="notifications">
          <template #title>
            <el-icon><Bell /></el-icon>
            <span>通知管理</span>
          </template>
          <el-menu-item index="/notifications">通知历史</el-menu-item>
          <el-menu-item index="/notifications/settings">通知设置</el-menu-item>
        </el-sub-menu>
        
        <el-menu-item index="/profile">
          <el-icon><UserFilled /></el-icon>
          <template #title>个人资料</template>
        </el-menu-item>
      </el-menu>
    </el-aside>

    <!-- 主内容区 -->
    <el-container>
      <!-- 顶部导航 -->
      <el-header class="header">
        <div class="header-left">
          <el-button
            text
            @click="toggleSidebar"
            class="collapse-btn"
          >
            <el-icon size="20">
              <Expand v-if="isCollapse" />
              <Fold v-else />
            </el-icon>
          </el-button>
          
          <el-breadcrumb separator="/">
            <el-breadcrumb-item
              v-for="item in breadcrumbs"
              :key="item.path"
              :to="item.path"
            >
              {{ item.title }}
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        
        <div class="header-right">
          <!-- 刷新按钮 -->
          <el-button
            text
            @click="refreshData"
            :loading="refreshLoading"
            class="refresh-btn"
          >
            <el-icon><Refresh /></el-icon>
          </el-button>
          
          <!-- 通知铃铛 -->
          <el-badge :value="unreadCount" :hidden="unreadCount === 0">
            <el-button text @click="showNotifications">
              <el-icon size="18"><Bell /></el-icon>
            </el-button>
          </el-badge>
          
          <!-- 用户菜单 -->
          <el-dropdown @command="handleUserCommand">
            <div class="user-info">
              <el-avatar :size="32" :src="userAvatar">
                <el-icon><UserFilled /></el-icon>
              </el-avatar>
              <span class="username">{{ authStore.user?.username }}</span>
              <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">
                  <el-icon><UserFilled /></el-icon>
                  个人资料
                </el-dropdown-item>
                <el-dropdown-item command="settings">
                  <el-icon><Setting /></el-icon>
                  设置
                </el-dropdown-item>
                <el-dropdown-item divided command="logout">
                  <el-icon><SwitchButton /></el-icon>
                  退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </el-header>

      <!-- 主内容 -->
      <el-main class="main-content">
        <router-view v-slot="{ Component }">
          <transition name="fade" mode="out-in">
            <component :is="Component" />
          </transition>
        </router-view>
      </el-main>
    </el-container>
  </el-container>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessageBox } from 'element-plus'
import { useAuthStore } from '@/stores/auth'
import { useStreamersStore } from '@/stores/streamers'
import { useNotificationsStore } from '@/stores/notifications'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()
const streamersStore = useStreamersStore()
const notificationsStore = useNotificationsStore()

// 侧边栏折叠状态
const isCollapse = ref(false)
const refreshLoading = ref(false)

// 当前激活的菜单
const activeMenu = computed(() => route.path)

// 面包屑导航
const breadcrumbs = computed(() => {
  const matched = route.matched.filter(item => item.meta?.title)
  return matched.map(item => ({
    title: item.meta?.title as string,
    path: item.path
  }))
})

// 用户头像
const userAvatar = computed(() => {
  // 这里可以根据用户信息生成头像URL
  return ''
})

// 未读通知数量
const unreadCount = computed(() => {
  // 这里可以计算未读通知数量
  return 0
})

// 切换侧边栏
const toggleSidebar = () => {
  isCollapse.value = !isCollapse.value
}

// 刷新数据
const refreshData = async () => {
  refreshLoading.value = true
  try {
    // 根据当前页面刷新相应数据
    if (route.path.includes('/streamers')) {
      await streamersStore.refreshAllStreamers()
    } else if (route.path.includes('/notifications')) {
      await notificationsStore.fetchHistory({ limit: 20 })
    }
  } catch (error) {
    console.error('刷新数据失败:', error)
  } finally {
    refreshLoading.value = false
  }
}

// 显示通知
const showNotifications = () => {
  router.push('/notifications')
}

// 处理用户菜单命令
const handleUserCommand = async (command: string) => {
  switch (command) {
    case 'profile':
      router.push('/profile')
      break
    case 'settings':
      router.push('/notifications/settings')
      break
    case 'logout':
      try {
        await ElMessageBox.confirm(
          '确定要退出登录吗？',
          '确认退出',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
        await authStore.logout()
      } catch {
        // 用户取消
      }
      break
  }
}

// 监听路由变化，自动刷新数据
watch(
  () => route.path,
  async (newPath) => {
    // 根据路由自动加载数据
    if (newPath === '/dashboard' || newPath.includes('/streamers')) {
      streamersStore.fetchStreamers()
    }
  },
  { immediate: true }
)
</script>

<style scoped>
.main-layout {
  height: 100vh;
}

.sidebar {
  background-color: var(--el-menu-bg-color);
  border-right: 1px solid var(--el-border-color-light);
  transition: width 0.3s ease;
}

.logo {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid var(--el-border-color-light);
  font-size: 18px;
  font-weight: bold;
  color: var(--el-color-primary);
}

.logo-text {
  margin-left: 8px;
}

.sidebar-menu {
  border-right: none;
}

.header {
  background-color: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color-light);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.collapse-btn {
  padding: 8px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.refresh-btn {
  padding: 8px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

.user-info:hover {
  background-color: var(--el-fill-color-light);
}

.username {
  font-size: 14px;
  color: var(--el-text-color-primary);
}

.dropdown-icon {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.main-content {
  background-color: var(--el-bg-color-page);
  padding: 20px;
  overflow-y: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header {
    padding: 0 12px;
  }
  
  .header-left {
    gap: 8px;
  }
  
  .header-right {
    gap: 8px;
  }
  
  .username {
    display: none;
  }
  
  .main-content {
    padding: 12px;
  }
}
</style>
