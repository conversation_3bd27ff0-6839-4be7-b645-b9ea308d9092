import axios from 'axios'
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { ElMessage } from 'element-plus'
import { useAuthStore } from '@/stores/auth'
import router from '@/router'

// API响应接口
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: {
    code: string
    message: string
    details?: any
  }
  timestamp: string
}

// 创建axios实例
const api: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 添加认证token
    const authStore = useAuthStore()
    if (authStore.token) {
      config.headers.Authorization = `Bearer ${authStore.token}`
    }
    
    // 添加请求时间戳
    config.metadata = { startTime: new Date() }
    
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    // 计算请求耗时
    const endTime = new Date()
    const duration = endTime.getTime() - response.config.metadata?.startTime?.getTime()
    console.log(`API请求耗时: ${duration}ms - ${response.config.method?.toUpperCase()} ${response.config.url}`)
    
    const { data } = response
    
    // 检查业务状态码
    if (data.success === false) {
      const errorMessage = data.error?.message || '请求失败'
      ElMessage.error(errorMessage)
      return Promise.reject(new Error(errorMessage))
    }
    
    return response
  },
  (error) => {
    console.error('API请求错误:', error)
    
    // 处理网络错误
    if (!error.response) {
      ElMessage.error('网络连接失败，请检查网络设置')
      return Promise.reject(error)
    }
    
    const { status, data } = error.response
    
    // 处理HTTP状态码
    switch (status) {
      case 401:
        ElMessage.error('登录已过期，请重新登录')
        const authStore = useAuthStore()
        authStore.logout()
        router.push('/login')
        break
      case 403:
        ElMessage.error('没有权限访问该资源')
        break
      case 404:
        ElMessage.error('请求的资源不存在')
        break
      case 422:
        const validationError = data?.error?.details?.[0]?.msg || '请求参数验证失败'
        ElMessage.error(validationError)
        break
      case 500:
        ElMessage.error('服务器内部错误，请稍后重试')
        break
      default:
        const errorMessage = data?.error?.message || `请求失败 (${status})`
        ElMessage.error(errorMessage)
    }
    
    return Promise.reject(error)
  }
)

// 通用请求方法
export const request = {
  get<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return api.get<ApiResponse<T>>(url, config).then(res => res.data.data!)
  },
  
  post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return api.post<ApiResponse<T>>(url, data, config).then(res => res.data.data!)
  },
  
  put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return api.put<ApiResponse<T>>(url, data, config).then(res => res.data.data!)
  },
  
  delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return api.delete<ApiResponse<T>>(url, config).then(res => res.data.data!)
  },
  
  patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return api.patch<ApiResponse<T>>(url, data, config).then(res => res.data.data!)
  }
}

export default api

// 扩展axios配置类型
declare module 'axios' {
  interface AxiosRequestConfig {
    metadata?: {
      startTime: Date
    }
  }
}
