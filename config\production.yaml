# 生产环境配置
app:
  name: "直播监控系统"
  version: "1.0.0"
  debug: false
  host: "0.0.0.0"
  port: 8000

database:
  url: "${DATABASE_URL}"
  echo: false  # 生产环境不显示SQL
  pool_size: 20
  max_overflow: 30

redis:
  url: "${REDIS_URL}"
  max_connections: 50

security:
  secret_key: "${SECRET_KEY}"
  algorithm: "HS256"
  access_token_expire_minutes: 30

monitoring:
  check_interval: 30
  max_concurrent_checks: 20
  retry_attempts: 3
  retry_delay: 5

notifications:
  wxpusher:
    app_token: "${WXPUSHER_APP_TOKEN}"
    base_url: "https://wxpusher.zjiecode.com/api"

  email:
    smtp_server: "${EMAIL_SMTP_SERVER}"
    smtp_port: "${EMAIL_SMTP_PORT}"
    username: "${EMAIL_USERNAME}"
    password: "${EMAIL_PASSWORD}"

platforms:
  bilibili:
    base_url: "https://api.live.bilibili.com"
    rate_limit: 60

  douyu:
    base_url: "https://open.douyucdn.cn/api"
    rate_limit: 60

  huya:
    base_url: "https://mp.huya.com"
    rate_limit: 60

logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "logs/app.log"
  max_bytes: 52428800  # 50MB
  backup_count: 10
